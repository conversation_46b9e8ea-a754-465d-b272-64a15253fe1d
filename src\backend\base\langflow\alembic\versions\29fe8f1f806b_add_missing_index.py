"""Add missing index

Revision ID: 29fe8f1f806b
Revises: 012fb73ac359
Create Date: 2024-05-21 09:23:48.772367

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.engine.reflection import Inspector

revision: str = "29fe8f1f806b"
down_revision: Union[str, None] = "012fb73ac359"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    inspector = sa.inspect(conn)  # type: ignore
    # ### commands auto generated by Alembic - please adjust! ###
    indexes = inspector.get_indexes("flow")
    with op.batch_alter_table("flow", schema=None) as batch_op:
        indexes_names = [index["name"] for index in indexes]
        if "ix_flow_folder_id" not in indexes_names:
            batch_op.create_index(batch_op.f("ix_flow_folder_id"), ["folder_id"], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    conn = op.get_bind()
    inspector = sa.inspect(conn)  # type: ignore
    # ### commands auto generated by Alembic - please adjust! ###
    indexes = inspector.get_indexes("flow")
    with op.batch_alter_table("flow", schema=None) as batch_op:
        indexes_names = [index["name"] for index in indexes]
        if "ix_flow_folder_id" in indexes_names:
            batch_op.drop_index(batch_op.f("ix_flow_folder_id"))

    # ### end Alembic commands ###
