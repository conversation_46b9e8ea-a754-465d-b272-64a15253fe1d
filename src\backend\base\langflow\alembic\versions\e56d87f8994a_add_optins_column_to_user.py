"""add_optins_column_to_user

Revision ID: e56d87f8994a
Revises: 1b8b740a6fa3
Create Date: 2025-04-09 15:57:46.904977

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.engine.reflection import Inspector
from langflow.utils import migration


# revision identifiers, used by Alembic.
revision: str = 'e56d87f8994a'
down_revision: Union[str, None] = '1b8b740a6fa3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    # ### commands auto generated by Alembic - please adjust! ###
    if not migration.column_exists(table_name='user', column_name='optins', conn=conn):
        with op.batch_alter_table('user', schema=None) as batch_op:
            batch_op.add_column(sa.Column('optins', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    conn = op.get_bind()
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('optins')
    # ### end Alembic commands ###
