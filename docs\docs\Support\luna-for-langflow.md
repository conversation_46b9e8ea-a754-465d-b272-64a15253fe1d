---
title: Enterprise support for Langflow
slug: /luna-for-langflow
---

With **<PERSON> for Langflow** support, you can develop and deploy Langflow applications with confidence.

Luna is a subscription to the Langflow expertise at DataStax. It's meant for Langflow users who want all the benefits of running their own open-source deployments, as well as the peace of mind that comes with having direct access to the team that has authored the majority of the Langflow code.

Luna subscribers can get help with general-purpose and technical questions for their open-source Langflow deployments.
If an issue is encountered, DataStax is there to help.

:::info
<!-- Last updated May 1 2025 -->
As of May 2025, Luna for Langflow support covers Langflow versions 1.4.x.

Subscribers must run a supported Python version to receive support.
Supported versions are `>=3.10, <3.14`, which includes all version from 3.10 through 3.13.x, but not 3.14.
:::

To subscribe or learn more, see [Luna for Langflow](https://www.datastax.com/products/luna-langflow).