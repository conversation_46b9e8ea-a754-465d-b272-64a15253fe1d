"""Add V2 File Table

Revision ID: dd9e0804ebd1
Revises: e3162c1804e6
Create Date: 2025-02-03 11:47:16.101523

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from langflow.utils import migration


# revision identifiers, used by Alembic.
revision: str = 'dd9e0804ebd1'
down_revision: Union[str, None] = 'e3162c1804e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    if not migration.table_exists("file", conn):
        # ### commands auto generated by Alembic - please adjust! ###
        op.create_table(
            "file",
            sa.Column("id", sqlmodel.sql.sqltypes.types.Uuid(), nullable=False),
            sa.Column("user_id", sqlmodel.sql.sqltypes.types.Uuid(), nullable=False),
            sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False, unique=True),
            sa.Column("path", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
            sa.Column("size", sa.Integer(), nullable=False),
            sa.Column("provider", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
            sa.Column("created_at", sa.DateTime(), nullable=False),
            sa.Column("updated_at", sa.DateTime(), nullable=False),
            sa.PrimaryKeyConstraint("id"),
            sa.ForeignKeyConstraint(["user_id"], ["user.id"], name="fk_file_user_id_user"),
            sa.UniqueConstraint("name"),
        )
        # ### end Alembic commands ###


def downgrade() -> None:
    conn = op.get_bind()
    # ### commands auto generated by Alembic - please adjust! ###
    if migration.table_exists("file", conn):
        op.drop_table("file")
    # ### end Alembic commands ###
