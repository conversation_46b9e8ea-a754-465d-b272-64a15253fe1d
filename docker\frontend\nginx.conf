server {
    gzip on;
    gzip_comp_level  2;
    gzip_min_length  1000;
    gzip_types  text/xml text/css;
    gzip_http_version 1.1;
    gzip_vary  on;
    gzip_disable "MSIE [4-6] \.";

    listen __FRONTEND_PORT__;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html =404;
        expires 1d;
        add_header Cache-Control "public";
    }
    location /api {
        proxy_pass __BACKEND_URL__;
    }
    location /health_check {
        proxy_pass __BACKEND_URL__;
    }
    location /health {
        proxy_pass __BACKEND_URL__;
    }

    include /etc/nginx/extra-conf.d/*.conf;
}
