---
title: Get help and request enhancements
slug: /contributing-github-issues
---

The Langflow GitHub repository is an integral part of the [Langflow community](/contributing-community).

In addition to general assistance with Langflow, the repository is the best place to report bugs and request enhancements to ensure that they are tracked by the Langflow project.

## GitHub issues

The [Issues page in the Langflow repository](https://github.com/langflow-ai/langflow/issues) is actively updated with bugs and feature requests.

:::tip
The best way to promote a request or bug is to comment on an existing issue.
Highly active issues are more likely to receive attention from contributors.

Before you report a bug or submit a feature request, search for existing similar issues.
Use the [Langflow repository's labels](https://github.com/langflow-ai/langflow/labels) to help filter your search.
:::

## GitHub discussions

If you need help with your code or Langflow in general, you can visit the [Langflow GitHub Discussions page](https://github.com/langflow-ai/langflow/discussions) or reach out through other [Langflow community](/contributing-community) channels.

The Langflow team doesn't provide individual support over email, and the team believes that public discussions help more users by virtue of their discoverability.

## Community guidelines and tips

Because the Issues and Discussion pages are public, the Langflow team asks that you follow these guidelines when submitting questions and issues:

* **Provide as many details as possible**: Simply stating that a feature doesn't work isn't helpful. The Langflow team needs details in order to recreate and find the issue.
* **Explain what exactly went wrong**: Including error messages and descriptions of _how_ your code failed, not just the fact that it failed.
* **Retrace your steps**: Explain what happened before the error, what you expected to happen instead of the error, and any recent changes you made, such as upgrading Langflow or a dependency.
* **Describe your environment**: Include your operating system, Langflow version, Python version, and any other environment-related details that could have contributed to the issue.
* **Include snippets of the code that failed**: Be sure to omit any sensitive values, and only provide parts relevant to the failure, rather than the entire script. Providing code snippets makes it much easier to reproduce errors, troubleshoot, and provide specific advice.
  * If your submission includes long sections of code, logs, or tracebacks, wrap them in [details tags](https://developer.mozilla.org/en/docs/Web/HTML/Element/details) (`<details> PASTE CODE HERE </details>`) to collapse the content and make it easier to read your submission.
* **Omit sensitive information**: Other than the information available on your public GitHub profile, don't include sensitive or personally identifying data, such as security keys, full names, personal identification numbers, addresses, and phone numbers.
* **Be kind**: Although bugs can be frustrating with any software, remember that your messages are read by real people who want to help. While you don't have to be saccharine, there's no need to be rude to get support.
  * Your issues and discussions are attached to your GitHub account, and they can be read by anyone on the internet, including current and potential employers and colleagues.
  * The Langlow repository is a public GitHub repository and, therefore, subject to the [GitHub Code of Conduct](https://docs.github.com/en/site-policy/github-terms/github-community-code-of-conduct).