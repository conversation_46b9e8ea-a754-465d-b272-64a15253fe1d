{"id": "b6de0fdb-31a2-40bf-b921-719bc0890a0e", "data": {"nodes": [{"id": "TextInput-iJPEJ", "type": "genericNode", "position": {"x": 94.43614181571661, "y": 387.24602783243165}, "data": {"type": "TextInput", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"Text Input\"\n    description = \"Get text inputs from the Playground.\"\n    icon = \"type\"\n    name = \"TextInput\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Text to be passed as input.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Text\", name=\"text\", method=\"text_response\"),\n    ]\n\n    def text_response(self) -> Message:\n        message = Message(\n            text=self.input_value,\n        )\n        return message\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "Good morning. Thanks for joining this project review meeting. We've got quite a few tasks to discuss, especially some Notion-related ones. Shall we get started?\n\nMorning, <PERSON>. Absolutely, let's dive in. I see we have several projects and tasks on our plate.\n\nGreat. Let's begin with the AI Content Gen project. I'm currently working on \"Montar base agente seletor de cortes.\" It's in progress, and I'm aiming to complete it by June 14th. Have you had a chance to look at this task, <PERSON><PERSON>?\n\nI haven't been directly involved with that one. Can you give me an overview of what it entails?\n\nOf course. Essentially, we're building a base agent that can intelligently select and edit content. It's part of our larger AI-driven content generation initiative. The challenge is creating an algorithm that can understand context and make smart editing decisions.\n\nInteresting. How's the progress so far?\n\nIt's coming along. I've set up the basic framework, but fine-tuning the selection criteria is proving to be more complex than initially anticipated. I might need an extra day or two beyond the June 14th deadline.\n\nUnderstood, <PERSON>. Keep me posted if you need any resources or if the deadline needs to be adjusted. By the way, I've been meaning to ask - have you had a chance to look into that new NLP library I mentioned last week? I think it could be useful for this project.\n\nActually, <PERSON><PERSON>, I haven't gotten to that yet. Should we add it as a new task? Maybe \"Evaluate NLP library for content selection\"?\n\nGood idea. Let's add that to our task list with a due date of next Friday. Now, moving on to the next task in this project - \"Create Notion Task Automation.\" It's assigned to you and set for June 19th, but you haven't started it yet, right? This is where I'd like to focus our discussion today.\n\nThat's correct. So, the goal is to streamline our workflow by automating certain tasks within Notion. I'm thinking we could create scripts or use Notion's API to automatically create, assign, and update tasks based on certain triggers or schedules.\n\nThat sounds like it could save us a lot of time. What specific automations are you considering?\n\nI'm glad you asked, Cezar. I'm thinking of a few key areas:\n1. Automatic task creation based on project milestones\n2. Assigning tasks to team members based on their expertise and current workload\n3. Updating task statuses based on linked database entries\n4. Generating weekly progress reports\n5. Setting up reminders for overdue tasks\n\nThose all sound valuable. Have you looked into the technical requirements for implementing these?\n\nI've done some initial research. Notion's API seems robust enough to handle these automations. We'll likely need to use a combination of Notion's API and a server to run our scripts. I'm thinking of using Node.js for this.\n\nGood thinking. Do you foresee any challenges?\n\nThe main challenge will be ensuring our automations are flexible enough to handle different project structures and team dynamics. We'll need to build in some configurability.\n\nAgreed. Let's make sure we involve the team in defining these automations. Their input will be crucial for making this truly useful. Oh, and speaking of team input, I think we should add a task for \"Conduct team survey on Notion pain points.\" This could help us prioritize which automations to tackle first.\n\nThat's an excellent idea, Cezar. I'll create that task and aim to complete the survey by next Wednesday. Now, I see we have another Notion-related task: \"Subir Notion Agent no Langflow Prod.\" Can you remind me what this entails?\n\nYes, this task is about deploying our Notion integration agent to the Langflow production environment. It's not started yet, but it's a crucial step in making our Notion automations available to the whole team.\n\nI see. What's the timeline for this?\n\nWe haven't set a specific deadline yet, but I think we should aim to complete this shortly after the automation task. Let's tentatively say by the end of June?\n\nSounds reasonable. Make sure to coordinate with the DevOps team for a smooth deployment. And while we're on the topic of deployment, we should probably add a task for \"Create documentation for Notion Agent usage.\" We want to make sure the team knows how to use these new tools once they're available.\n\nYou're right, Felipe. I'll add that to our task list. Now, switching gears a bit, let's talk about the Internal Projects. I see you're working on \"Crypto Links\" - it's in progress.\n\nAh yes, our blockchain initiative. It's moving forward. I'm researching various blockchain platforms and their potential applications for our projects. I'm particularly interested in smart contract capabilities.\n\nInteresting. Keep me updated on any promising findings. By the way, have you considered reaching out to any blockchain experts for consultation? It might be worth adding a task for \"Schedule blockchain expert consultation.\"\n\nThat's a great suggestion, Cezar. I'll add it to my to-do list. Now, for the Internal Tasks, I see you're assigned to \"Revisar modos do Charlinho, preparar para open source.\" What's the status on that?\n\nI haven't started yet, but it's on my radar. The deadline is June 7th, so I'll be diving into it this week. Essentially, we need to review and refine Charlinho's modes before we open-source the project.\n\nSounds good. Let me know if you need any assistance with that. Oh, and don't forget we need to add a task for \"Prepare Charlinho documentation for open source.\" We want to make sure our project is well-documented when we release it.\n\nYou're right, Felipe. I'll make sure to include that in our task list. Now, I see you have several tasks assigned to you in the Internal Tasks section. Can you give me a quick rundown?\n\nOf course. I'm working on finding a freelancer to create flows in ComfyUI - that's in progress and due May 28th. I'm also handling the conception of the Agent UI, due May 30th. Both are moving along well.\n\nThere's also a task to \"Check, install and test Gladia to use a bot in Google Meet.\" That's in progress, and I'm collaborating with C on it.\n\nThat's quite a workload. How are you managing all these tasks?\n\nIt's challenging, but I'm prioritizing based on deadlines and dependencies. The Notion automation project is a high priority because it'll help us manage tasks more efficiently in the long run.\n\nGood strategy, Felipe. Is there anything you need from me or the team to help move these tasks forward?\n\nActually, yes. For the \"pegar os arquivos necessários para tentarmos montar um stinger com ffmpeg\" task, I could use some input on which files are critical for this. It's a low-priority task due June 2nd, but any insights would be helpful.\n\nI'll review our asset library and send you a list of potential files by tomorrow. Oh, and let's add a task for \"Create ffmpeg stinger tutorial\" once we figure out the process. It could be useful for the team in the future.\n\nGreat idea, Cezar. I'll add that to our backlog. Anything else we should discuss?\n\nI think we've covered the major points. Oh, one last thing - for the \"Create Notion Task Automation\" project, I was thinking of setting up a series of short daily meetings next week to keep everyone aligned. What do you think?\n\nThat's a good idea. Maybe 15-minute stand-ups? We can use those to address any roadblocks quickly. And let's add a task for \"Set up Notion Automation progress tracking board\" to help visualize our progress during these stand-ups.\n\nPerfect. I'll send out calendar invites this afternoon and create that tracking board task. Any final thoughts or concerns, Cezar?\n\nNot from my side. I think we have a clear path forward, especially with the Notion-related tasks and the new items we've added to our list.\n\nAgreed. Let's plan to reconvene next week to check on progress, particularly for the Notion automation project and these new tasks we've discussed. Thanks for the comprehensive update, Felipe.\n\nThank you, Cezar. I'll send out a summary of our discussion and action items shortly, including all the new tasks we've identified during this meeting.\n", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Text to be passed as input.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Get text inputs from the Playground.", "icon": "type", "base_classes": ["Message"], "display_name": "Meeting Transcript", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Text", "method": "text_response", "value": "__UNDEFINED__", "cache": true}], "field_order": ["input_value"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "TextInput-iJPEJ"}, "selected": false, "width": 384, "height": 302, "dragging": false, "positionAbsolute": {"x": 94.43614181571661, "y": 387.24602783243165}}, {"id": "NotionUserList-TvIKS", "type": "genericNode", "position": {"x": 80.49204196902156, "y": 741.0568511678105}, "data": {"type": "NotionUserList", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import requests\nfrom typing import List, Dict\nfrom pydantic import BaseModel\nfrom langflow.io import Output\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\n\n\nclass NotionUserList(LCToolComponent):\n    display_name = \"List Users \"\n    description = \"Retrieve users from Notion.\"\n    documentation = \"https://docs.langflow.org/integrations/notion/list-users\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionUserListSchema(BaseModel):\n        pass\n\n    def run_model(self) -> List[Data]:\n        users = self._list_users()\n        records = []\n        combined_text = \"\"\n\n        for user in users:\n            output = \"User:\\n\"\n            for key, value in user.items():\n                output += f\"{key.replace('_', ' ').title()}: {value}\\n\"\n            output += \"________________________\\n\"\n\n            combined_text += output\n            records.append(Data(text=output, data=user))\n\n        self.status = records\n        return records\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"notion_list_users\",\n            description=\"Retrieve users from Notion.\",\n            func=self._list_users,\n            args_schema=self.NotionUserListSchema,\n        )\n\n    def _list_users(self) -> List[Dict]:\n        url = \"https://api.notion.com/v1/users\"\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Notion-Version\": \"2022-06-28\",\n        }\n\n        response = requests.get(url, headers=headers)\n        response.raise_for_status()\n\n        data = response.json()\n        results = data[\"results\"]\n\n        users = []\n        for user in results:\n            user_data = {\n                \"id\": user[\"id\"],\n                \"type\": user[\"type\"],\n                \"name\": user.get(\"name\", \"\"),\n                \"avatar_url\": user.get(\"avatar_url\", \"\"),\n            }\n            users.append(user_data)\n\n        return users\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}}, "description": "Retrieve users from Notion.", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "List Users ", "documentation": "https://docs.langflow.org/integrations/notion/list-users", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true, "hidden": true}], "field_order": ["notion_secret"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionUserList-TvIKS", "description": "Retrieve users from Notion.", "display_name": "List Users "}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 80.49204196902156, "y": 741.0568511678105}, "dragging": false}, {"id": "NotionSearch-M66HF", "type": "genericNode", "position": {"x": 1095.6934863134345, "y": 407.8718765800806}, "data": {"type": "NotionSearch", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import requests\nfrom typing import Dict, Any, List\nfrom pydantic import BaseModel, Field\nfrom langflow.io import Output\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput, StrInput, DropdownInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\n\n\nclass NotionSearch(LCToolComponent):\n    display_name: str = \"Search \"\n    description: str = \"Searches all pages and databases that have been shared with an integration. The search field can be an empty value to show all values from that search\"\n    documentation: str = \"https://docs.langflow.org/integrations/notion/search\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n        StrInput(\n            name=\"query\",\n            display_name=\"Search Query\",\n            info=\"The text that the API compares page and database titles against.\",\n        ),\n        DropdownInput(\n            name=\"filter_value\",\n            display_name=\"Filter Type\",\n            info=\"Limits the results to either only pages or only databases.\",\n            options=[\"page\", \"database\"],\n            value=\"page\",\n        ),\n        DropdownInput(\n            name=\"sort_direction\",\n            display_name=\"Sort Direction\",\n            info=\"The direction to sort the results.\",\n            options=[\"ascending\", \"descending\"],\n            value=\"descending\",\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionSearchSchema(BaseModel):\n        query: str = Field(..., description=\"The search query text.\")\n        filter_value: str = Field(default=\"page\", description=\"Filter type: 'page' or 'database'.\")\n        sort_direction: str = Field(default=\"descending\", description=\"Sort direction: 'ascending' or 'descending'.\")\n\n    def run_model(self) -> List[Data]:\n        results = self._search_notion(self.query, self.filter_value, self.sort_direction)\n        records = []\n        combined_text = f\"Results found: {len(results)}\\n\\n\"\n\n        for result in results:\n            result_data = {\n                \"id\": result[\"id\"],\n                \"type\": result[\"object\"],\n                \"last_edited_time\": result[\"last_edited_time\"],\n            }\n\n            if result[\"object\"] == \"page\":\n                result_data[\"title_or_url\"] = result[\"url\"]\n                text = f\"id: {result['id']}\\ntitle_or_url: {result['url']}\\n\"\n            elif result[\"object\"] == \"database\":\n                if \"title\" in result and isinstance(result[\"title\"], list) and len(result[\"title\"]) > 0:\n                    result_data[\"title_or_url\"] = result[\"title\"][0][\"plain_text\"]\n                    text = f\"id: {result['id']}\\ntitle_or_url: {result['title'][0]['plain_text']}\\n\"\n                else:\n                    result_data[\"title_or_url\"] = \"N/A\"\n                    text = f\"id: {result['id']}\\ntitle_or_url: N/A\\n\"\n\n            text += f\"type: {result['object']}\\nlast_edited_time: {result['last_edited_time']}\\n\\n\"\n            combined_text += text\n            records.append(Data(text=text, data=result_data))\n\n        self.status = records\n        return records\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"notion_search\",\n            description=\"Search Notion pages and databases. Input should include the search query and optionally filter type and sort direction.\",\n            func=self._search_notion,\n            args_schema=self.NotionSearchSchema,\n        )\n\n    def _search_notion(\n        self, query: str, filter_value: str = \"page\", sort_direction: str = \"descending\"\n    ) -> List[Dict[str, Any]]:\n        url = \"https://api.notion.com/v1/search\"\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Content-Type\": \"application/json\",\n            \"Notion-Version\": \"2022-06-28\",\n        }\n\n        data = {\n            \"query\": query,\n            \"filter\": {\"value\": filter_value, \"property\": \"object\"},\n            \"sort\": {\"direction\": sort_direction, \"timestamp\": \"last_edited_time\"},\n        }\n\n        response = requests.post(url, headers=headers, json=data)\n        response.raise_for_status()\n\n        results = response.json()\n        return results[\"results\"]\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "filter_value": {"trace_as_metadata": true, "options": ["page", "database"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "filter_value", "value": "database", "display_name": "Filter Type", "advanced": true, "dynamic": false, "info": "Limits the results to either only pages or only databases.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}, "query": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "query", "value": "", "display_name": "Search Query", "advanced": true, "dynamic": false, "info": "The text that the API compares page and database titles against.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "sort_direction": {"trace_as_metadata": true, "options": ["ascending", "descending"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "sort_direction", "value": "descending", "display_name": "Sort Direction", "advanced": true, "dynamic": false, "info": "The direction to sort the results.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}}, "description": "Searches all pages and databases that have been shared with an integration. The search field can be an empty value to show all values from that search", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "Search ", "documentation": "https://docs.langflow.org/integrations/notion/search", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true, "hidden": false}], "field_order": ["notion_secret", "query", "filter_value", "sort_direction"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionSearch-M66HF", "description": "Searches all pages and databases that have been shared with an integration.", "display_name": "Search "}, "selected": false, "width": 384, "height": 386, "positionAbsolute": {"x": 1095.6934863134345, "y": 407.8718765800806}, "dragging": false}, {"id": "Prompt-19rub", "type": "genericNode", "position": {"x": 688.7954025956392, "y": 456.4686463487848}, "data": {"type": "Prompt", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"Prompt\"\n    description: str = \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"\n    trace_type = \"prompt\"\n    name = \"Prompt\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"Template\"),\n    ]\n\n    outputs = [\n        Output(display_name=\"Prompt Message\", name=\"prompt\", method=\"build_prompt\"),\n    ]\n\n    async def build_prompt(\n        self,\n    ) -> Message:\n        prompt = Message.from_template_and_variables(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    def post_code_processing(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"\n        This function is called after the code validation is done.\n        \"\"\"\n        frontend_node = super().post_code_processing(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "template": {"trace_as_input": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "template", "value": "<Instructions>\nYou are an AI assistant specialized in analyzing meeting transcripts and identifying tasks. Your goal is to extract relevant tasks from the given transcript, search for related existing tasks in Notion, and provide a comprehensive list of tasks with their current status and any needed updates.\n\nYou have access to the following input:\n\n<transcript>\n{TRANSCRIPT}\n</transcript>\n\n<users>\n{USERS}\n</users>\n\nFollow these steps to complete your task:\n\n1. Carefully read through the transcript and identify any mentioned tasks, action items, or follow-ups.\n\n2. For each identified task:\n   a. Use the notion_search tool to find if there's an existing related task in Notion.\n   b. If a related task is found, note its ID and current status.\n   c. If no related task is found, mark it as a new task.\n\n3. For each task (existing or new), determine:\n   a. The task name or description\n   b. The assigned person (if mentioned)\n   c. The current status (for existing tasks) or suggested status (for new tasks)\n   d. Any updates or changes mentioned in the transcript\n\n4. Compile your findings into a list of tasks using the following format:\n\n<task_list>\n<task>\n<id>[Notion page ID if existing, or \"NEW\" if new task]</id>\n<name>[Task name or description]</name>\n<assignee>[Assigned person, if mentioned]</assignee>\n<status>[Current status for existing tasks, or suggested status for new tasks]</status>\n<updates>[Any updates or changes mentioned in the transcript]</updates>\n</task>\n</task_list>\n\nRemember to focus on tasks that are directly related to the meeting discussion. Do not include general conversation topics or unrelated mentions as tasks.\n\nProvide your final output in the format specified above, with each task enclosed in its own <task></task> tags within the overall <task_list></task_list> structure.\n\nToday is: {CURRENT_DATE}\n</Instructions>\n\n\n", "display_name": "Template", "advanced": false, "dynamic": false, "info": "", "title_case": false, "type": "prompt", "_input_type": "PromptInput"}, "TRANSCRIPT": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "TRANSCRIPT", "display_name": "TRANSCRIPT", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}, "USERS": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "USERS", "display_name": "USERS", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}, "CURRENT_DATE": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "CURRENT_DATE", "display_name": "CURRENT_DATE", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}}, "description": "Create a prompt template with dynamic variables.", "icon": "prompts", "is_input": null, "is_output": null, "is_composition": null, "base_classes": ["Message"], "name": "", "display_name": "Prompt", "documentation": "", "custom_fields": {"template": ["TRANSCRIPT", "USERS", "CURRENT_DATE"]}, "output_types": [], "full_path": null, "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "prompt", "hidden": null, "display_name": "Prompt Message", "method": "build_prompt", "value": "__UNDEFINED__", "cache": true}], "field_order": ["template"], "beta": false, "error": null, "edited": false, "lf_version": "1.0.17"}, "id": "Prompt-19rub"}, "selected": false, "width": 384, "height": 588, "positionAbsolute": {"x": 688.7954025956392, "y": 456.4686463487848}, "dragging": false}, {"id": "ParseData-aNk1v", "type": "genericNode", "position": {"x": 540.4151030255898, "y": 834.2819856588019}, "data": {"type": "ParseData", "node": {"template": {"_type": "Component", "data": {"trace_as_metadata": true, "list": false, "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Parse Data\"\n    description = \"Convert Data into plain text following a specified template.\"\n    icon = \"braces\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\"),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(display_name=\"Text\", name=\"text\", method=\"parse_data\"),\n    ]\n\n    def parse_data(self) -> Message:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n\n        result_string = data_to_text(template, data, sep=self.sep)\n        self.status = result_string\n        return Message(text=result_string)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "template", "value": "{text}", "display_name": "Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data into plain text following a specified template.", "icon": "braces", "base_classes": ["Message"], "display_name": "Parse Data", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Text", "method": "parse_data", "value": "__UNDEFINED__", "cache": true}], "field_order": ["data", "template", "sep"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "ParseData-aNk1v", "showNode": false}, "selected": false, "width": 96, "height": 96, "dragging": false, "positionAbsolute": {"x": 540.4151030255898, "y": 834.2819856588019}}, {"id": "ToolCallingAgent-rVWeq", "type": "genericNode", "position": {"x": 1566.291217492157, "y": 583.6687094567968}, "data": {"type": "ToolCallingAgent", "node": {"template": {"_type": "Component", "chat_history": {"trace_as_metadata": true, "list": true, "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "chat_history", "value": "", "display_name": "Chat History", "advanced": true, "input_types": ["Data"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataInput"}, "llm": {"trace_as_metadata": true, "list": false, "required": true, "placeholder": "", "show": true, "name": "llm", "value": "", "display_name": "Language Model", "advanced": false, "input_types": ["LanguageModel"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "HandleInput"}, "tools": {"trace_as_metadata": true, "list": true, "required": false, "placeholder": "", "show": true, "name": "tools", "value": "", "display_name": "Tools", "advanced": false, "input_types": ["Tool", "BaseTool"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "HandleInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Optional, List\n\nfrom langchain.agents import create_tool_calling_agent\nfrom langchain_core.prompts import ChatPromptTemplate, PromptTemplate, HumanMessagePromptTemplate\nfrom langflow.base.agents.agent import LCToolsAgentComponent\nfrom langflow.inputs import MultilineInput\nfrom langflow.inputs.inputs import HandleInput, DataInput\nfrom langflow.schema import Data\n\n\nclass ToolCallingAgentComponent(LCToolsAgentComponent):\n    display_name: str = \"Tool Calling Agent\"\n    description: str = \"Agent that uses tools\"\n    icon = \"LangChain\"\n    beta = True\n    name = \"ToolCallingAgent\"\n\n    inputs = LCToolsAgentComponent._base_inputs + [\n        HandleInput(name=\"llm\", display_name=\"Language Model\", input_types=[\"LanguageModel\"], required=True),\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"System Prompt\",\n            info=\"System prompt for the agent.\",\n            value=\"You are a helpful assistant\",\n        ),\n        MultilineInput(\n            name=\"user_prompt\", display_name=\"Prompt\", info=\"This prompt must contain 'input' key.\", value=\"{input}\"\n        ),\n        DataInput(name=\"chat_history\", display_name=\"Chat History\", is_list=True, advanced=True),\n    ]\n\n    def get_chat_history_data(self) -> Optional[List[Data]]:\n        return self.chat_history\n\n    def create_agent_runnable(self):\n        if \"input\" not in self.user_prompt:\n            raise ValueError(\"Prompt must contain 'input' key.\")\n        messages = [\n            (\"system\", self.system_prompt),\n            (\"placeholder\", \"{chat_history}\"),\n            HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=[\"input\"], template=self.user_prompt)),\n            (\"placeholder\", \"{agent_scratchpad}\"),\n        ]\n        prompt = ChatPromptTemplate.from_messages(messages)\n        return create_tool_calling_agent(self.llm, self.tools, prompt)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "handle_parsing_errors": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "handle_parsing_errors", "value": true, "display_name": "<PERSON><PERSON> Parse Errors", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "input_value": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "Analyze this meeting", "display_name": "Input", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "max_iterations": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "max_iterations", "value": 15, "display_name": "Max Iterations", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "int", "_input_type": "IntInput"}, "system_prompt": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "system_prompt", "value": "", "display_name": "System Prompt", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "System prompt for the agent.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "user_prompt": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "user_prompt", "value": "{input}", "display_name": "Prompt", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "This prompt must contain 'input' key.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "verbose": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "verbose", "value": true, "display_name": "Verbose", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "bool", "_input_type": "BoolInput"}}, "description": "Agent that uses tools", "icon": "<PERSON><PERSON><PERSON><PERSON>", "base_classes": ["AgentExecutor", "Message"], "display_name": "Tool Calling Agent", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["AgentExecutor"], "selected": "AgentExecutor", "name": "agent", "display_name": "Agent", "method": "build_agent", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Message"], "selected": "Message", "name": "response", "display_name": "Response", "method": "message_response", "value": "__UNDEFINED__", "cache": true}], "field_order": ["input_value", "handle_parsing_errors", "verbose", "max_iterations", "tools", "llm", "system_prompt", "user_prompt", "chat_history"], "beta": true, "edited": false, "lf_version": "1.0.17"}, "id": "ToolCallingAgent-rVWeq"}, "selected": false, "width": 384, "height": 398, "positionAbsolute": {"x": 1566.291217492157, "y": 583.6687094567968}, "dragging": false}, {"id": "OpenAIModel-Ht8xI", "type": "genericNode", "position": {"x": 1097.0545781920632, "y": 805.60631548423}, "data": {"type": "OpenAIModel", "node": {"template": {"_type": "Component", "api_key": {"load_from_db": false, "required": false, "placeholder": "", "show": true, "name": "api_key", "value": "", "display_name": "OpenAI API Key", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The OpenAI API Key to use for the OpenAI model.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import operator\nfrom functools import reduce\n\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.inputs import (\n    BoolInput,\n    DictInput,\n    DropdownInput,\n    FloatInput,\n    IntInput,\n    SecretStrInput,\n    StrInput,\n)\n\n\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"\n    description = \"Generates text using OpenAI LLMs.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIModel\"\n\n    inputs = LCModelComponent._base_inputs + [\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"Max Tokens\",\n            advanced=True,\n            info=\"The maximum number of tokens to generate. Set to 0 for unlimited tokens.\",\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(name=\"model_kwargs\", display_name=\"Model Kwargs\", advanced=True),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON Mode\",\n            advanced=True,\n            info=\"If True, it will output JSON regardless of passing a schema.\",\n        ),\n        DictInput(\n            name=\"output_schema\",\n            is_list=True,\n            display_name=\"Schema\",\n            advanced=True,\n            info=\"The schema for the Output of the model. You must pass the word JSON in the prompt. If left blank, JSON mode will be disabled.\",\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            advanced=False,\n            options=OPENAI_MODEL_NAMES,\n            value=OPENAI_MODEL_NAMES[0],\n        ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API Base\",\n            advanced=True,\n            info=\"The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\",\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API Key\",\n            info=\"The OpenAI API Key to use for the OpenAI model.\",\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n        ),\n        FloatInput(name=\"temperature\", display_name=\"Temperature\", value=0.1),\n        IntInput(\n            name=\"seed\",\n            display_name=\"Seed\",\n            info=\"The seed controls the reproducibility of the job.\",\n            advanced=True,\n            value=1,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        # self.output_schema is a list of dictionaries\n        # let's convert it to a dictionary\n        output_schema_dict: dict[str, str] = reduce(operator.ior, self.output_schema or {}, {})\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = bool(output_schema_dict) or self.json_mode\n        seed = self.seed\n\n        if openai_api_key:\n            api_key = SecretStr(openai_api_key)\n        else:\n            api_key = None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n        )\n        if json_mode:\n            if output_schema_dict:\n                output = output.with_structured_output(schema=output_schema_dict, method=\"json_mode\")  # type: ignore\n            else:\n                output = output.bind(response_format={\"type\": \"json_object\"})  # type: ignore\n\n        return output  # type: ignore\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"\n        Get a message from an OpenAI exception.\n\n        Args:\n            exception (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")  # type: ignore\n            if message:\n                return message\n        return\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "", "display_name": "Input", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "json_mode": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "json_mode", "value": false, "display_name": "JSON Mode", "advanced": true, "dynamic": false, "info": "If True, it will output JSON regardless of passing a schema.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "max_tokens": {"trace_as_metadata": true, "range_spec": {"step_type": "float", "min": 0, "max": 128000, "step": 0.1}, "list": false, "required": false, "placeholder": "", "show": true, "name": "max_tokens", "value": "", "display_name": "<PERSON>", "advanced": true, "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "title_case": false, "type": "int", "_input_type": "IntInput"}, "model_kwargs": {"trace_as_input": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "model_kwargs", "value": {}, "display_name": "Model Kwargs", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "dict", "_input_type": "DictInput"}, "model_name": {"trace_as_metadata": true, "options": ["gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-0125"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "model_name", "value": "gpt-4o", "display_name": "Model Name", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "openai_api_base": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "openai_api_base", "value": "", "display_name": "OpenAI API Base", "advanced": true, "dynamic": false, "info": "The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "output_schema": {"trace_as_input": true, "list": true, "required": false, "placeholder": "", "show": true, "name": "output_schema", "value": {}, "display_name": "<PERSON><PERSON><PERSON>", "advanced": true, "dynamic": false, "info": "The schema for the Output of the model. You must pass the word JSON in the prompt. If left blank, JSON mode will be disabled.", "title_case": false, "type": "dict", "_input_type": "DictInput"}, "seed": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "seed", "value": 1, "display_name": "Seed", "advanced": true, "dynamic": false, "info": "The seed controls the reproducibility of the job.", "title_case": false, "type": "int", "_input_type": "IntInput"}, "stream": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "stream", "value": false, "display_name": "Stream", "advanced": true, "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "system_message": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "system_message", "value": "", "display_name": "System Message", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "System message to pass to the model.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "temperature": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "temperature", "value": 0.1, "display_name": "Temperature", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "float", "_input_type": "FloatInput"}}, "description": "Generates text using OpenAI LLMs.", "icon": "OpenAI", "base_classes": ["LanguageModel", "Message"], "display_name": "OpenAI", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text_output", "display_name": "Text", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["LanguageModel"], "selected": "LanguageModel", "name": "model_output", "display_name": "Language Model", "method": "build_model", "value": "__UNDEFINED__", "cache": true}], "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "output_schema", "model_name", "openai_api_base", "api_key", "temperature", "seed"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "OpenAIModel-Ht8xI"}, "selected": false, "width": 384, "height": 302, "dragging": false, "positionAbsolute": {"x": 1097.0545781920632, "y": 805.60631548423}}, {"id": "Prompt-Lbxk6", "type": "genericNode", "position": {"x": 3042.6844997246735, "y": 416.83992118486856}, "data": {"type": "Prompt", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"Prompt\"\n    description: str = \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"\n    trace_type = \"prompt\"\n    name = \"Prompt\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"Template\"),\n    ]\n\n    outputs = [\n        Output(display_name=\"Prompt Message\", name=\"prompt\", method=\"build_prompt\"),\n    ]\n\n    async def build_prompt(\n        self,\n    ) -> Message:\n        prompt = Message.from_template_and_variables(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    def post_code_processing(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"\n        This function is called after the code validation is done.\n        \"\"\"\n        frontend_node = super().post_code_processing(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "template": {"trace_as_input": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "template", "value": "<Instructions>\nYou are an AI assistant responsible for updating tasks in Notion based on the information provided from a meeting analysis. Your goal is to create new tasks and update existing ones using the Notion API tools available to you, and then provide a summary in a simple markdown format suitable for a chat interface.\n\nYou have access to the following inputs:\n\n<task_list>\n{TASK_LIST}\n</task_list>\n\n<databases>\n{DATABASES}\n</databases>\n\n<users>\n{USERS}\n</users>\n\nFollow these steps to update the tasks in Notion and generate a markdown summary:\n\n1. Identify the Task database ID from the provided <databases> list.\n\n2. Before processing any tasks, retrieve the database properties for the Task database:\n   a. Use the notion_database_properties and carefully review the properties, their types, and any options for select or multi-select properties.\n   b. Pay attention to the properties format for further usage.\n\n3. For each task in the task list:\n   a. If the task ID is \"NEW\", create a new task using the create_notion_page tool.\n   b. If the task has an existing ID, update the task using the update_notion_page tool.\n   c. Remember to use the properties from the DB retrieved from the notion_database_properties tool\n\n4. When creating a new task:\n   a. Use the create_notion_page tool.\n   b. Include the task name, assignee (if available), status, and any other relevant properties based on the database structure.\n   c. Ensure that the property names and types match exactly with what you retrieved from the notion_database_properties call.\n\n5. When updating an existing task:\n   a. Use the update_notion_page tool.\n   b. Update the status, assignee, or any other relevant properties mentioned in the <updates> field.\n   c. Ensure that the property names and types match exactly with what you retrieved from the notion_database_properties call.\n\n6. After each function call, wait for the <function_result> before proceeding to the next task.\n\n7. If you encounter any errors during the process, note them and continue with the next task.\n\n8. Provide a summary of your actions for each task in a simple markdown format. Use the following structure:\n   # Task Update Summary\n\n   ## Created Tasks\n   - **[Task Name]**: Assigned to [Assignee], Status: [Status]\n     - Details: [Brief description of the new task]\n\n   ## Updated Tasks\n   - **[Task Name]** (ID: [Notion Page ID])\n     - Changes: [Brief description of changes]\n     - Status: [Success/Error]\n\n   ## Errors\n   - **[Task Name or ID]**: [Description of the error encountered]\n\n\nRemember to use the exact property names, types, and options as specified in the Notion database properties you retrieved at the beginning. This is crucial for ensuring that all updates and creations are done correctly.\n\nIf you encounter any errors or uncertainties, include them in the Errors section of the markdown summary. With enough detail to the user understand the issues.\n\nProvide your final output as a complete markdown document containing all the tasks you've processed, whether they were created, updated, or encountered errors. Use only basic markdown formatting (headers, bold, lists) to ensure compatibility with chat interfaces. Do not include any XML tags or complex formatting in your final output.\n\nToday is: {CURRENT_DATE}\n\n</Instructions>", "display_name": "Template", "advanced": false, "dynamic": false, "info": "", "title_case": false, "type": "prompt", "_input_type": "PromptInput"}, "TASK_LIST": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "TASK_LIST", "display_name": "TASK_LIST", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}, "DATABASES": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "DATABASES", "display_name": "DATABASES", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}, "USERS": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "USERS", "display_name": "USERS", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}, "CURRENT_DATE": {"field_type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "", "fileTypes": [], "file_path": "", "password": false, "name": "CURRENT_DATE", "display_name": "CURRENT_DATE", "advanced": false, "input_types": ["Message", "Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "type": "str"}}, "description": "Create a prompt template with dynamic variables.", "icon": "prompts", "is_input": null, "is_output": null, "is_composition": null, "base_classes": ["Message"], "name": "", "display_name": "Prompt", "documentation": "", "custom_fields": {"template": ["TASK_LIST", "DATABASES", "USERS", "CURRENT_DATE"]}, "output_types": [], "full_path": null, "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "prompt", "hidden": null, "display_name": "Prompt Message", "method": "build_prompt", "value": "__UNDEFINED__", "cache": true}], "field_order": ["template"], "beta": false, "error": null, "edited": false}, "id": "Prompt-Lbxk6"}, "selected": false, "width": 384, "height": 674, "positionAbsolute": {"x": 3042.6844997246735, "y": 416.83992118486856}, "dragging": false}, {"id": "ToolCallingAgent-GurdE", "type": "genericNode", "position": {"x": 3974.1377259893243, "y": 867.4647271037014}, "data": {"type": "ToolCallingAgent", "node": {"template": {"_type": "Component", "chat_history": {"trace_as_metadata": true, "list": true, "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "chat_history", "value": "", "display_name": "Chat History", "advanced": true, "input_types": ["Data"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "DataInput"}, "llm": {"trace_as_metadata": true, "list": false, "required": true, "placeholder": "", "show": true, "name": "llm", "value": "", "display_name": "Language Model", "advanced": false, "input_types": ["LanguageModel"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "HandleInput"}, "tools": {"trace_as_metadata": true, "list": true, "required": false, "placeholder": "", "show": true, "name": "tools", "value": "", "display_name": "Tools", "advanced": false, "input_types": ["Tool", "BaseTool"], "dynamic": false, "info": "", "title_case": false, "type": "other", "_input_type": "HandleInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Optional, List\n\nfrom langchain.agents import create_tool_calling_agent\nfrom langchain_core.prompts import ChatPromptTemplate, PromptTemplate, HumanMessagePromptTemplate\nfrom langflow.base.agents.agent import LCToolsAgentComponent\nfrom langflow.inputs import MultilineInput\nfrom langflow.inputs.inputs import HandleInput, DataInput\nfrom langflow.schema import Data\n\n\nclass ToolCallingAgentComponent(LCToolsAgentComponent):\n    display_name: str = \"Tool Calling Agent\"\n    description: str = \"Agent that uses tools\"\n    icon = \"LangChain\"\n    beta = True\n    name = \"ToolCallingAgent\"\n\n    inputs = LCToolsAgentComponent._base_inputs + [\n        HandleInput(name=\"llm\", display_name=\"Language Model\", input_types=[\"LanguageModel\"], required=True),\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"System Prompt\",\n            info=\"System prompt for the agent.\",\n            value=\"You are a helpful assistant\",\n        ),\n        MultilineInput(\n            name=\"user_prompt\", display_name=\"Prompt\", info=\"This prompt must contain 'input' key.\", value=\"{input}\"\n        ),\n        DataInput(name=\"chat_history\", display_name=\"Chat History\", is_list=True, advanced=True),\n    ]\n\n    def get_chat_history_data(self) -> Optional[List[Data]]:\n        return self.chat_history\n\n    def create_agent_runnable(self):\n        if \"input\" not in self.user_prompt:\n            raise ValueError(\"Prompt must contain 'input' key.\")\n        messages = [\n            (\"system\", self.system_prompt),\n            (\"placeholder\", \"{chat_history}\"),\n            HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=[\"input\"], template=self.user_prompt)),\n            (\"placeholder\", \"{agent_scratchpad}\"),\n        ]\n        prompt = ChatPromptTemplate.from_messages(messages)\n        return create_tool_calling_agent(self.llm, self.tools, prompt)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "handle_parsing_errors": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "handle_parsing_errors", "value": true, "display_name": "<PERSON><PERSON> Parse Errors", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "input_value": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "Do your task.", "display_name": "Input", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "max_iterations": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "max_iterations", "value": 15, "display_name": "Max Iterations", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "int", "_input_type": "IntInput"}, "system_prompt": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "system_prompt", "value": "", "display_name": "System Prompt", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "System prompt for the agent.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "user_prompt": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "user_prompt", "value": "{input}", "display_name": "Prompt", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "This prompt must contain 'input' key.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "verbose": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "verbose", "value": true, "display_name": "Verbose", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "bool", "_input_type": "BoolInput"}}, "description": "Agent that uses tools", "icon": "<PERSON><PERSON><PERSON><PERSON>", "base_classes": ["AgentExecutor", "Message"], "display_name": "Tool Calling Agent", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["AgentExecutor"], "selected": "AgentExecutor", "name": "agent", "display_name": "Agent", "method": "build_agent", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Message"], "selected": "Message", "name": "response", "display_name": "Response", "method": "message_response", "value": "__UNDEFINED__", "cache": true}], "field_order": ["input_value", "handle_parsing_errors", "verbose", "max_iterations", "tools", "llm", "system_prompt", "user_prompt", "chat_history"], "beta": true, "edited": false, "lf_version": "1.0.17"}, "id": "ToolCallingAgent-GurdE"}, "selected": false, "width": 384, "height": 398, "positionAbsolute": {"x": 3974.1377259893243, "y": 867.4647271037014}, "dragging": false}, {"id": "OpenAIModel-OTfnt", "type": "genericNode", "position": {"x": 3513.5648778762093, "y": 710.2099422974287}, "data": {"type": "OpenAIModel", "node": {"template": {"_type": "Component", "api_key": {"load_from_db": false, "required": false, "placeholder": "", "show": true, "name": "api_key", "value": "", "display_name": "OpenAI API Key", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The OpenAI API Key to use for the OpenAI model.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import operator\nfrom functools import reduce\n\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.inputs import (\n    BoolInput,\n    DictInput,\n    DropdownInput,\n    FloatInput,\n    IntInput,\n    SecretStrInput,\n    StrInput,\n)\n\n\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"\n    description = \"Generates text using OpenAI LLMs.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIModel\"\n\n    inputs = LCModelComponent._base_inputs + [\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"Max Tokens\",\n            advanced=True,\n            info=\"The maximum number of tokens to generate. Set to 0 for unlimited tokens.\",\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(name=\"model_kwargs\", display_name=\"Model Kwargs\", advanced=True),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON Mode\",\n            advanced=True,\n            info=\"If True, it will output JSON regardless of passing a schema.\",\n        ),\n        DictInput(\n            name=\"output_schema\",\n            is_list=True,\n            display_name=\"Schema\",\n            advanced=True,\n            info=\"The schema for the Output of the model. You must pass the word JSON in the prompt. If left blank, JSON mode will be disabled.\",\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            advanced=False,\n            options=OPENAI_MODEL_NAMES,\n            value=OPENAI_MODEL_NAMES[0],\n        ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API Base\",\n            advanced=True,\n            info=\"The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\",\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API Key\",\n            info=\"The OpenAI API Key to use for the OpenAI model.\",\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n        ),\n        FloatInput(name=\"temperature\", display_name=\"Temperature\", value=0.1),\n        IntInput(\n            name=\"seed\",\n            display_name=\"Seed\",\n            info=\"The seed controls the reproducibility of the job.\",\n            advanced=True,\n            value=1,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        # self.output_schema is a list of dictionaries\n        # let's convert it to a dictionary\n        output_schema_dict: dict[str, str] = reduce(operator.ior, self.output_schema or {}, {})\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = bool(output_schema_dict) or self.json_mode\n        seed = self.seed\n\n        if openai_api_key:\n            api_key = SecretStr(openai_api_key)\n        else:\n            api_key = None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n        )\n        if json_mode:\n            if output_schema_dict:\n                output = output.with_structured_output(schema=output_schema_dict, method=\"json_mode\")  # type: ignore\n            else:\n                output = output.bind(response_format={\"type\": \"json_object\"})  # type: ignore\n\n        return output  # type: ignore\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"\n        Get a message from an OpenAI exception.\n\n        Args:\n            exception (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")  # type: ignore\n            if message:\n                return message\n        return\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "", "display_name": "Input", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "MessageInput"}, "json_mode": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "json_mode", "value": false, "display_name": "JSON Mode", "advanced": true, "dynamic": false, "info": "If True, it will output JSON regardless of passing a schema.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "max_tokens": {"trace_as_metadata": true, "range_spec": {"step_type": "float", "min": 0, "max": 128000, "step": 0.1}, "list": false, "required": false, "placeholder": "", "show": true, "name": "max_tokens", "value": "", "display_name": "<PERSON>", "advanced": true, "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "title_case": false, "type": "int", "_input_type": "IntInput"}, "model_kwargs": {"trace_as_input": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "model_kwargs", "value": {}, "display_name": "Model Kwargs", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "dict", "_input_type": "DictInput"}, "model_name": {"trace_as_metadata": true, "options": ["gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-0125"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "model_name", "value": "gpt-4o", "display_name": "Model Name", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "openai_api_base": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "openai_api_base", "value": "", "display_name": "OpenAI API Base", "advanced": true, "dynamic": false, "info": "The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "output_schema": {"trace_as_input": true, "list": true, "required": false, "placeholder": "", "show": true, "name": "output_schema", "value": {}, "display_name": "<PERSON><PERSON><PERSON>", "advanced": true, "dynamic": false, "info": "The schema for the Output of the model. You must pass the word JSON in the prompt. If left blank, JSON mode will be disabled.", "title_case": false, "type": "dict", "_input_type": "DictInput"}, "seed": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "seed", "value": 1, "display_name": "Seed", "advanced": true, "dynamic": false, "info": "The seed controls the reproducibility of the job.", "title_case": false, "type": "int", "_input_type": "IntInput"}, "stream": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "stream", "value": false, "display_name": "Stream", "advanced": true, "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}, "system_message": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "system_message", "value": "", "display_name": "System Message", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "System message to pass to the model.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "temperature": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "temperature", "value": 0.1, "display_name": "Temperature", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "float", "_input_type": "FloatInput"}}, "description": "Generates text using OpenAI LLMs.", "icon": "OpenAI", "base_classes": ["LanguageModel", "Message"], "display_name": "OpenAI", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text_output", "display_name": "Text", "method": "text_response", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["LanguageModel"], "selected": "LanguageModel", "name": "model_output", "display_name": "Language Model", "method": "build_model", "value": "__UNDEFINED__", "cache": true}], "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "output_schema", "model_name", "openai_api_base", "api_key", "temperature", "seed"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "OpenAIModel-OTfnt"}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 3513.5648778762093, "y": 710.2099422974287}, "dragging": false}, {"id": "AddContentToPage-vrAvx", "type": "genericNode", "position": {"x": 2649.2991466550634, "y": 1050.6250104897197}, "data": {"type": "AddContentToPage", "node": {"template": {"_type": "Component", "block_id": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "block_id", "value": "", "display_name": "Page/Block ID", "advanced": true, "dynamic": false, "info": "The ID of the page/block to add the content.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import json\nfrom typing import Dict, Any, <PERSON>\nfrom markdown import markdown\nfrom bs4 import BeautifulSoup\nimport requests\nfrom langflow.io import Output\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput, StrInput, MultilineInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\nfrom pydantic import BaseModel, Field\n\n\nclass AddContentToPage(LCToolComponent):\n    display_name: str = \"Add Content to Page \"\n    description: str = \"Convert markdown text to Notion blocks and append them to a Notion page.\"\n    documentation: str = \"https://developers.notion.com/reference/patch-block-children\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        MultilineInput(\n            name=\"markdown_text\",\n            display_name=\"Markdown Text\",\n            info=\"The markdown text to convert to Notion blocks.\",\n        ),\n        StrInput(\n            name=\"block_id\",\n            display_name=\"Page/Block ID\",\n            info=\"The ID of the page/block to add the content.\",\n        ),\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class AddContentToPageSchema(BaseModel):\n        markdown_text: str = Field(..., description=\"The markdown text to convert to Notion blocks.\")\n        block_id: str = Field(..., description=\"The ID of the page/block to add the content.\")\n\n    def run_model(self) -> Data:\n        result = self._add_content_to_page(self.markdown_text, self.block_id)\n        return Data(data=result, text=json.dumps(result))\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"add_content_to_notion_page\",\n            description=\"Convert markdown text to Notion blocks and append them to a Notion page.\",\n            func=self._add_content_to_page,\n            args_schema=self.AddContentToPageSchema,\n        )\n\n    def _add_content_to_page(self, markdown_text: str, block_id: str) -> Union[Dict[str, Any], str]:\n        try:\n            html_text = markdown(markdown_text)\n            soup = BeautifulSoup(html_text, \"html.parser\")\n            blocks = self.process_node(soup)\n\n            url = f\"https://api.notion.com/v1/blocks/{block_id}/children\"\n            headers = {\n                \"Authorization\": f\"Bearer {self.notion_secret}\",\n                \"Content-Type\": \"application/json\",\n                \"Notion-Version\": \"2022-06-28\",\n            }\n\n            data = {\n                \"children\": blocks,\n            }\n\n            response = requests.patch(url, headers=headers, json=data)\n            response.raise_for_status()\n\n            return response.json()\n        except requests.exceptions.RequestException as e:\n            error_message = f\"Error: Failed to add content to Notion page. {str(e)}\"\n            if hasattr(e, \"response\") and e.response is not None:\n                error_message += f\" Status code: {e.response.status_code}, Response: {e.response.text}\"\n            return error_message\n        except Exception as e:\n            return f\"Error: An unexpected error occurred while adding content to Notion page. {str(e)}\"\n\n    def process_node(self, node):\n        blocks = []\n        if isinstance(node, str):\n            text = node.strip()\n            if text:\n                if text.startswith(\"#\"):\n                    heading_level = text.count(\"#\", 0, 6)\n                    heading_text = text[heading_level:].strip()\n                    if heading_level == 1:\n                        blocks.append(self.create_block(\"heading_1\", heading_text))\n                    elif heading_level == 2:\n                        blocks.append(self.create_block(\"heading_2\", heading_text))\n                    elif heading_level == 3:\n                        blocks.append(self.create_block(\"heading_3\", heading_text))\n                else:\n                    blocks.append(self.create_block(\"paragraph\", text))\n        elif node.name == \"h1\":\n            blocks.append(self.create_block(\"heading_1\", node.get_text(strip=True)))\n        elif node.name == \"h2\":\n            blocks.append(self.create_block(\"heading_2\", node.get_text(strip=True)))\n        elif node.name == \"h3\":\n            blocks.append(self.create_block(\"heading_3\", node.get_text(strip=True)))\n        elif node.name == \"p\":\n            code_node = node.find(\"code\")\n            if code_node:\n                code_text = code_node.get_text()\n                language, code = self.extract_language_and_code(code_text)\n                blocks.append(self.create_block(\"code\", code, language=language))\n            elif self.is_table(str(node)):\n                blocks.extend(self.process_table(node))\n            else:\n                blocks.append(self.create_block(\"paragraph\", node.get_text(strip=True)))\n        elif node.name == \"ul\":\n            blocks.extend(self.process_list(node, \"bulleted_list_item\"))\n        elif node.name == \"ol\":\n            blocks.extend(self.process_list(node, \"numbered_list_item\"))\n        elif node.name == \"blockquote\":\n            blocks.append(self.create_block(\"quote\", node.get_text(strip=True)))\n        elif node.name == \"hr\":\n            blocks.append(self.create_block(\"divider\", \"\"))\n        elif node.name == \"img\":\n            blocks.append(self.create_block(\"image\", \"\", image_url=node.get(\"src\")))\n        elif node.name == \"a\":\n            blocks.append(self.create_block(\"bookmark\", node.get_text(strip=True), link_url=node.get(\"href\")))\n        elif node.name == \"table\":\n            blocks.extend(self.process_table(node))\n\n        for child in node.children:\n            if isinstance(child, str):\n                continue\n            blocks.extend(self.process_node(child))\n\n        return blocks\n\n    def extract_language_and_code(self, code_text):\n        lines = code_text.split(\"\\n\")\n        language = lines[0].strip()\n        code = \"\\n\".join(lines[1:]).strip()\n        return language, code\n\n    def is_code_block(self, text):\n        return text.startswith(\"```\")\n\n    def extract_code_block(self, text):\n        lines = text.split(\"\\n\")\n        language = lines[0].strip(\"`\").strip()\n        code = \"\\n\".join(lines[1:]).strip(\"`\").strip()\n        return language, code\n\n    def is_table(self, text):\n        rows = text.split(\"\\n\")\n        if len(rows) < 2:\n            return False\n\n        has_separator = False\n        for i, row in enumerate(rows):\n            if \"|\" in row:\n                cells = [cell.strip() for cell in row.split(\"|\")]\n                cells = [cell for cell in cells if cell]  # Remove empty cells\n                if i == 1 and all(set(cell) <= set(\"-|\") for cell in cells):\n                    has_separator = True\n                elif not cells:\n                    return False\n\n        return has_separator and len(rows) >= 3\n\n    def process_list(self, node, list_type):\n        blocks = []\n        for item in node.find_all(\"li\"):\n            item_text = item.get_text(strip=True)\n            checked = item_text.startswith(\"[x]\")\n            is_checklist = item_text.startswith(\"[ ]\") or checked\n\n            if is_checklist:\n                item_text = item_text.replace(\"[x]\", \"\").replace(\"[ ]\", \"\").strip()\n                blocks.append(self.create_block(\"to_do\", item_text, checked=checked))\n            else:\n                blocks.append(self.create_block(list_type, item_text))\n        return blocks\n\n    def process_table(self, node):\n        blocks = []\n        header_row = node.find(\"thead\").find(\"tr\") if node.find(\"thead\") else None\n        body_rows = node.find(\"tbody\").find_all(\"tr\") if node.find(\"tbody\") else []\n\n        if header_row or body_rows:\n            table_width = max(\n                len(header_row.find_all([\"th\", \"td\"])) if header_row else 0,\n                max(len(row.find_all([\"th\", \"td\"])) for row in body_rows),\n            )\n\n            table_block = self.create_block(\"table\", \"\", table_width=table_width, has_column_header=bool(header_row))\n            blocks.append(table_block)\n\n            if header_row:\n                header_cells = [cell.get_text(strip=True) for cell in header_row.find_all([\"th\", \"td\"])]\n                header_row_block = self.create_block(\"table_row\", header_cells)\n                blocks.append(header_row_block)\n\n            for row in body_rows:\n                cells = [cell.get_text(strip=True) for cell in row.find_all([\"th\", \"td\"])]\n                row_block = self.create_block(\"table_row\", cells)\n                blocks.append(row_block)\n\n        return blocks\n\n    def create_block(self, block_type: str, content: str, **kwargs) -> Dict[str, Any]:\n        block: dict[str, Any] = {\n            \"object\": \"block\",\n            \"type\": block_type,\n            block_type: {},\n        }\n\n        if block_type in [\n            \"paragraph\",\n            \"heading_1\",\n            \"heading_2\",\n            \"heading_3\",\n            \"bulleted_list_item\",\n            \"numbered_list_item\",\n            \"quote\",\n        ]:\n            block[block_type][\"rich_text\"] = [\n                {\n                    \"type\": \"text\",\n                    \"text\": {\n                        \"content\": content,\n                    },\n                }\n            ]\n        elif block_type == \"to_do\":\n            block[block_type][\"rich_text\"] = [\n                {\n                    \"type\": \"text\",\n                    \"text\": {\n                        \"content\": content,\n                    },\n                }\n            ]\n            block[block_type][\"checked\"] = kwargs.get(\"checked\", False)\n        elif block_type == \"code\":\n            block[block_type][\"rich_text\"] = [\n                {\n                    \"type\": \"text\",\n                    \"text\": {\n                        \"content\": content,\n                    },\n                }\n            ]\n            block[block_type][\"language\"] = kwargs.get(\"language\", \"plain text\")\n        elif block_type == \"image\":\n            block[block_type] = {\"type\": \"external\", \"external\": {\"url\": kwargs.get(\"image_url\", \"\")}}\n        elif block_type == \"divider\":\n            pass\n        elif block_type == \"bookmark\":\n            block[block_type][\"url\"] = kwargs.get(\"link_url\", \"\")\n        elif block_type == \"table\":\n            block[block_type][\"table_width\"] = kwargs.get(\"table_width\", 0)\n            block[block_type][\"has_column_header\"] = kwargs.get(\"has_column_header\", False)\n            block[block_type][\"has_row_header\"] = kwargs.get(\"has_row_header\", False)\n        elif block_type == \"table_row\":\n            block[block_type][\"cells\"] = [[{\"type\": \"text\", \"text\": {\"content\": cell}} for cell in content]]\n\n        return block\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "markdown_text": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "markdown_text", "value": "", "display_name": "Markdown Text", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The markdown text to convert to Notion blocks.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}}, "description": "Convert markdown text to Notion blocks and append them to a Notion page.", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "Add Content to Page ", "documentation": "https://developers.notion.com/reference/patch-block-children", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true}], "field_order": ["markdown_text", "block_id", "notion_secret"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "AddContentToPage-vrAvx", "description": "Convert markdown text to Notion blocks and append them to a Notion page.", "display_name": "Add Content to Page "}, "selected": false, "width": 384, "height": 330, "positionAbsolute": {"x": 2649.2991466550634, "y": 1050.6250104897197}, "dragging": false}, {"id": "NotionPageCreator-Exc7f", "type": "genericNode", "position": {"x": 3050.8201437255634, "y": 1391.0449862668834}, "data": {"type": "NotionPageCreator", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import json\nfrom typing import Dict, Any, Union\nimport requests\nfrom pydantic import BaseModel, Field\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput, StrInput, MultilineInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\nfrom langflow.io import Output\n\nclass NotionPageCreator(LCToolComponent):\n    display_name: str = \"Create Page \"\n    description: str = \"A component for creating Notion pages.\"\n    documentation: str = \"https://docs.langflow.org/integrations/notion/page-create\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        StrInput(\n            name=\"database_id\",\n            display_name=\"Database ID\",\n            info=\"The ID of the Notion database.\",\n        ),\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n        MultilineInput(\n            name=\"properties_json\",\n            display_name=\"Properties (JSON)\",\n            info=\"The properties of the new page as a JSON string.\",\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionPageCreatorSchema(BaseModel):\n        database_id: str = Field(..., description=\"The ID of the Notion database.\")\n        properties_json: str = Field(..., description=\"The properties of the new page as a JSON string.\")\n\n    def run_model(self) -> Data:\n        result = self._create_notion_page(self.database_id, self.properties_json)\n        if isinstance(result, str):\n            # An error occurred, return it as text\n            return Data(text=result)\n        else:\n            # Success, return the created page data\n            output = \"Created page properties:\\n\"\n            for prop_name, prop_value in result.get(\"properties\", {}).items():\n                output += f\"{prop_name}: {prop_value}\\n\"\n            return Data(text=output, data=result)\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"create_notion_page\",\n            description=\"Create a new page in a Notion database. IMPORTANT: Use the tool to check the Database properties for more details before using this tool.\",\n            func=self._create_notion_page,\n            args_schema=self.NotionPageCreatorSchema,\n        )\n\n    def _create_notion_page(self, database_id: str, properties_json: str) -> Union[Dict[str, Any], str]:\n        if not database_id or not properties_json:\n            return \"Invalid input. Please provide 'database_id' and 'properties_json'.\"\n\n        try:\n            properties = json.loads(properties_json)\n        except json.JSONDecodeError as e:\n            return f\"Invalid properties format. Please provide a valid JSON string. Error: {str(e)}\"\n\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Content-Type\": \"application/json\",\n            \"Notion-Version\": \"2022-06-28\",\n        }\n\n        data = {\n            \"parent\": {\"database_id\": database_id},\n            \"properties\": properties,\n        }\n\n        try:\n            response = requests.post(\"https://api.notion.com/v1/pages\", headers=headers, json=data)\n            response.raise_for_status()\n            result = response.json()\n            return result\n        except requests.exceptions.RequestException as e:\n            error_message = f\"Failed to create Notion page. Error: {str(e)}\"\n            if hasattr(e, \"response\") and e.response is not None:\n                error_message += f\" Status code: {e.response.status_code}, Response: {e.response.text}\"\n            return error_message\n\n    def __call__(self, *args, **kwargs):\n        return self._create_notion_page(*args, **kwargs)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "database_id": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "database_id", "value": "", "display_name": "Database ID", "advanced": true, "dynamic": false, "info": "The ID of the Notion database.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}, "properties_json": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "properties_json", "value": "", "display_name": "Properties (JSON)", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The properties of the new page as a JSON string.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "A component for creating Notion pages.", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "Create Page ", "documentation": "https://docs.langflow.org/integrations/notion/page-create", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true}], "field_order": ["database_id", "notion_secret", "properties_json"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionPageCreator-Exc7f", "description": "A component for creating Notion pages.", "display_name": "Create Page "}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 3050.8201437255634, "y": 1391.0449862668834}, "dragging": false}, {"id": "NotionDatabaseProperties-IjzLV", "type": "genericNode", "position": {"x": 3053.0023230574693, "y": 1061.535907149244}, "data": {"type": "NotionDatabaseProperties", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import requests\nfrom typing import Dict, Union\nfrom pydantic import BaseModel, Field\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput, StrInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\nfrom langflow.io import Output\n\nclass NotionDatabaseProperties(LCToolComponent):\n    display_name: str = \"List Database Properties \"\n    description: str = \"Retrieve properties of a Notion database.\"\n    documentation: str = \"https://docs.langflow.org/integrations/notion/list-database-properties\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        StrInput(\n            name=\"database_id\",\n            display_name=\"Database ID\",\n            info=\"The ID of the Notion database.\",\n        ),\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionDatabasePropertiesSchema(BaseModel):\n        database_id: str = Field(..., description=\"The ID of the Notion database.\")\n\n    def run_model(self) -> Data:\n        result = self._fetch_database_properties(self.database_id)\n        if isinstance(result, str):\n            # An error occurred, return it as text\n            return Data(text=result)\n        else:\n            # Success, return the properties\n            return Data(text=str(result), data=result)\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"notion_database_properties\",\n            description=\"Retrieve properties of a Notion database. Input should include the database ID.\",\n            func=self._fetch_database_properties,\n            args_schema=self.NotionDatabasePropertiesSchema,\n        )\n\n    def _fetch_database_properties(self, database_id: str) -> Union[Dict, str]:\n        url = f\"https://api.notion.com/v1/databases/{database_id}\"\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Notion-Version\": \"2022-06-28\",  # Use the latest supported version\n        }\n        try:\n            response = requests.get(url, headers=headers)\n            response.raise_for_status()\n            data = response.json()\n            properties = data.get(\"properties\", {})\n            return properties\n        except requests.exceptions.RequestException as e:\n            return f\"Error fetching Notion database properties: {str(e)}\"\n        except ValueError as e:\n            return f\"Error parsing Notion API response: {str(e)}\"\n        except Exception as e:\n            return f\"An unexpected error occurred: {str(e)}\"\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "database_id": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "database_id", "value": "", "display_name": "Database ID", "advanced": true, "dynamic": false, "info": "The ID of the Notion database.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}}, "description": "Retrieve properties of a Notion database.", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "List Database Properties ", "documentation": "https://docs.langflow.org/integrations/notion/list-database-properties", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true}], "field_order": ["database_id", "notion_secret"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionDatabaseProperties-IjzLV", "description": "Retrieve properties of a Notion database.", "display_name": "List Database Properties "}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 3053.0023230574693, "y": 1061.535907149244}, "dragging": false}, {"id": "NotionPageUpdate-bexvy", "type": "genericNode", "position": {"x": 2649.2991466550625, "y": 1385.262204377853}, "data": {"type": "NotionPageUpdate", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import json\nimport requests\nfrom typing import Dict, Any, Union\nfrom pydantic import BaseModel, Field\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput, StrInput, MultilineInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\nfrom loguru import logger\nfrom langflow.io import Output\n\nclass NotionPageUpdate(LCToolComponent):\n    display_name: str = \"Update Page Property \"\n    description: str = \"Update the properties of a Notion page.\"\n    documentation: str = \"https://docs.langflow.org/integrations/notion/page-update\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        StrInput(\n            name=\"page_id\",\n            display_name=\"Page ID\",\n            info=\"The ID of the Notion page to update.\",\n        ),\n        MultilineInput(\n            name=\"properties\",\n            display_name=\"Properties\",\n            info=\"The properties to update on the page (as a JSON string or a dictionary).\",\n        ),\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionPageUpdateSchema(BaseModel):\n        page_id: str = Field(..., description=\"The ID of the Notion page to update.\")\n        properties: Union[str, Dict[str, Any]] = Field(\n            ..., description=\"The properties to update on the page (as a JSON string or a dictionary).\"\n        )\n\n    def run_model(self) -> Data:\n        result = self._update_notion_page(self.page_id, self.properties)\n        if isinstance(result, str):\n            # An error occurred, return it as text\n            return Data(text=result)\n        else:\n            # Success, return the updated page data\n            output = \"Updated page properties:\\n\"\n            for prop_name, prop_value in result.get(\"properties\", {}).items():\n                output += f\"{prop_name}: {prop_value}\\n\"\n            return Data(text=output, data=result)\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"update_notion_page\",\n            description=\"Update the properties of a Notion page. IMPORTANT: Use the tool to check the Database properties for more details before using this tool.\",\n            func=self._update_notion_page,\n            args_schema=self.NotionPageUpdateSchema,\n        )\n\n    def _update_notion_page(self, page_id: str, properties: Union[str, Dict[str, Any]]) -> Union[Dict[str, Any], str]:\n        url = f\"https://api.notion.com/v1/pages/{page_id}\"\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Content-Type\": \"application/json\",\n            \"Notion-Version\": \"2022-06-28\",  # Use the latest supported version\n        }\n\n        # Parse properties if it's a string\n        if isinstance(properties, str):\n            try:\n                parsed_properties = json.loads(properties)\n            except json.JSONDecodeError as e:\n                error_message = f\"Invalid JSON format for properties: {str(e)}\"\n                logger.error(error_message)\n                return error_message\n\n        else:\n            parsed_properties = properties\n\n        data = {\"properties\": parsed_properties}\n\n        try:\n            logger.info(f\"Sending request to Notion API: URL: {url}, Data: {json.dumps(data)}\")\n            response = requests.patch(url, headers=headers, json=data)\n            response.raise_for_status()\n            updated_page = response.json()\n\n            logger.info(f\"Successfully updated Notion page. Response: {json.dumps(updated_page)}\")\n            return updated_page\n        except requests.exceptions.HTTPError as e:\n            error_message = f\"HTTP Error occurred: {str(e)}\"\n            if e.response is not None:\n                error_message += f\"\\nStatus code: {e.response.status_code}\"\n                error_message += f\"\\nResponse body: {e.response.text}\"\n            logger.error(error_message)\n            return error_message\n        except requests.exceptions.RequestException as e:\n            error_message = f\"An error occurred while making the request: {str(e)}\"\n            logger.error(error_message)\n            return error_message\n        except Exception as e:\n            error_message = f\"An unexpected error occurred: {str(e)}\"\n            logger.error(error_message)\n            return error_message\n\n    def __call__(self, *args, **kwargs):\n        return self._update_notion_page(*args, **kwargs)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}, "page_id": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "page_id", "value": "", "display_name": "Page ID", "advanced": true, "dynamic": false, "info": "The ID of the Notion page to update.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "properties": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "properties", "value": "", "display_name": "Properties", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The properties to update on the page (as a JSON string or a dictionary).", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Update the properties of a Notion page.", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "Update Page Property ", "documentation": "https://docs.langflow.org/integrations/notion/page-update", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true, "hidden": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true}], "field_order": ["page_id", "properties", "notion_secret"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionPageUpdate-bexvy", "description": "Update the properties of a Notion page.", "display_name": "Update Page Property "}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 2649.2991466550625, "y": 1385.262204377853}, "dragging": false}, {"id": "NotionSearch-EdSJb", "type": "genericNode", "position": {"x": 2435.4455721283834, "y": 357.45573905064634}, "data": {"type": "NotionSearch", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import requests\nfrom typing import Dict, Any, List\nfrom pydantic import BaseModel, Field\nfrom langflow.io import Output\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput, StrInput, DropdownInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\n\n\nclass NotionSearch(LCToolComponent):\n    display_name: str = \"Search \"\n    description: str = \"Searches all pages and databases that have been shared with an integration. The search field can be an empty value to show all values from that search\"\n    documentation: str = \"https://docs.langflow.org/integrations/notion/search\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n        StrInput(\n            name=\"query\",\n            display_name=\"Search Query\",\n            info=\"The text that the API compares page and database titles against.\",\n        ),\n        DropdownInput(\n            name=\"filter_value\",\n            display_name=\"Filter Type\",\n            info=\"Limits the results to either only pages or only databases.\",\n            options=[\"page\", \"database\"],\n            value=\"page\",\n        ),\n        DropdownInput(\n            name=\"sort_direction\",\n            display_name=\"Sort Direction\",\n            info=\"The direction to sort the results.\",\n            options=[\"ascending\", \"descending\"],\n            value=\"descending\",\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionSearchSchema(BaseModel):\n        query: str = Field(..., description=\"The search query text.\")\n        filter_value: str = Field(default=\"page\", description=\"Filter type: 'page' or 'database'.\")\n        sort_direction: str = Field(default=\"descending\", description=\"Sort direction: 'ascending' or 'descending'.\")\n\n    def run_model(self) -> List[Data]:\n        results = self._search_notion(self.query, self.filter_value, self.sort_direction)\n        records = []\n        combined_text = f\"Results found: {len(results)}\\n\\n\"\n\n        for result in results:\n            result_data = {\n                \"id\": result[\"id\"],\n                \"type\": result[\"object\"],\n                \"last_edited_time\": result[\"last_edited_time\"],\n            }\n\n            if result[\"object\"] == \"page\":\n                result_data[\"title_or_url\"] = result[\"url\"]\n                text = f\"id: {result['id']}\\ntitle_or_url: {result['url']}\\n\"\n            elif result[\"object\"] == \"database\":\n                if \"title\" in result and isinstance(result[\"title\"], list) and len(result[\"title\"]) > 0:\n                    result_data[\"title_or_url\"] = result[\"title\"][0][\"plain_text\"]\n                    text = f\"id: {result['id']}\\ntitle_or_url: {result['title'][0]['plain_text']}\\n\"\n                else:\n                    result_data[\"title_or_url\"] = \"N/A\"\n                    text = f\"id: {result['id']}\\ntitle_or_url: N/A\\n\"\n\n            text += f\"type: {result['object']}\\nlast_edited_time: {result['last_edited_time']}\\n\\n\"\n            combined_text += text\n            records.append(Data(text=text, data=result_data))\n\n        self.status = records\n        return records\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"notion_search\",\n            description=\"Search Notion pages and databases. Input should include the search query and optionally filter type and sort direction.\",\n            func=self._search_notion,\n            args_schema=self.NotionSearchSchema,\n        )\n\n    def _search_notion(\n        self, query: str, filter_value: str = \"page\", sort_direction: str = \"descending\"\n    ) -> List[Dict[str, Any]]:\n        url = \"https://api.notion.com/v1/search\"\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Content-Type\": \"application/json\",\n            \"Notion-Version\": \"2022-06-28\",\n        }\n\n        data = {\n            \"query\": query,\n            \"filter\": {\"value\": filter_value, \"property\": \"object\"},\n            \"sort\": {\"direction\": sort_direction, \"timestamp\": \"last_edited_time\"},\n        }\n\n        response = requests.post(url, headers=headers, json=data)\n        response.raise_for_status()\n\n        results = response.json()\n        return results[\"results\"]\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "filter_value": {"trace_as_metadata": true, "options": ["page", "database"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "filter_value", "value": "database", "display_name": "Filter Type", "advanced": true, "dynamic": false, "info": "Limits the results to either only pages or only databases.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}, "query": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "query", "value": "", "display_name": "Search Query", "advanced": true, "dynamic": false, "info": "The text that the API compares page and database titles against.", "title_case": false, "type": "str", "_input_type": "StrInput"}, "sort_direction": {"trace_as_metadata": true, "options": ["ascending", "descending"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "sort_direction", "value": "descending", "display_name": "Sort Direction", "advanced": true, "dynamic": false, "info": "The direction to sort the results.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}}, "description": "List All Databases", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "List Databases", "documentation": "https://docs.langflow.org/integrations/notion/search", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true, "hidden": false}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true, "hidden": true}], "field_order": ["notion_secret", "query", "filter_value", "sort_direction"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionSearch-EdSJb", "description": "Searches all pages and databases that have been shared with an integration.", "display_name": "Search "}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 2435.4455721283834, "y": 357.45573905064634}, "dragging": false}, {"id": "ParseData-vYVwu", "type": "genericNode", "position": {"x": 2871.5903532688335, "y": 563.1965154816405}, "data": {"type": "ParseData", "node": {"template": {"_type": "Component", "data": {"trace_as_metadata": true, "list": false, "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Parse Data\"\n    description = \"Convert Data into plain text following a specified template.\"\n    icon = \"braces\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\"),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(display_name=\"Text\", name=\"text\", method=\"parse_data\"),\n    ]\n\n    def parse_data(self) -> Message:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n\n        result_string = data_to_text(template, data, sep=self.sep)\n        self.status = result_string\n        return Message(text=result_string)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "template", "value": "{text}", "display_name": "Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data into plain text following a specified template.", "icon": "braces", "base_classes": ["Message"], "display_name": "Parse Data", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Text", "method": "parse_data", "value": "__UNDEFINED__", "cache": true}], "field_order": ["data", "template", "sep"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "ParseData-vYVwu", "showNode": false}, "selected": false, "width": 96, "height": 96, "positionAbsolute": {"x": 2871.5903532688335, "y": 563.1965154816405}, "dragging": false}, {"id": "ChatOutput-zBv53", "type": "genericNode", "position": {"x": 4429.812566227955, "y": 940.6072472757681}, "data": {"type": "ChatOutput", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.memory import store_message\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import MESSAGE_SENDER_AI, MESSAGE_SENDER_NAME_AI, MESSAGE_SENDER_USER\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    icon = \"ChatOutput\"\n    name = \"ChatOutput\"\n\n    inputs = [\n        MessageTextInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Message to be passed as output.\",\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"message\", method=\"message_response\"),\n    ]\n\n    def message_response(self) -> Message:\n        message = Message(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n        )\n        if (\n            self.session_id\n            and isinstance(message, Message)\n            and isinstance(message.text, str)\n            and self.should_store_message\n        ):\n            store_message(\n                message,\n                flow_id=self.graph.flow_id,\n            )\n            self.message.value = message\n\n        self.status = message\n        return message\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "data_template": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "data_template", "value": "{text}", "display_name": "Data Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "input_value": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "input_value", "value": "", "display_name": "Text", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "Message to be passed as output.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "sender": {"trace_as_metadata": true, "options": ["Machine", "User"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "sender", "value": "Machine", "display_name": "Sender Type", "advanced": true, "dynamic": false, "info": "Type of sender.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}, "sender_name": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "sender_name", "value": "AI", "display_name": "Sender Name", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "Name of the sender.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "session_id": {"trace_as_input": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "session_id", "value": "", "display_name": "Session ID", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "title_case": false, "type": "str", "_input_type": "MessageTextInput"}, "should_store_message": {"trace_as_metadata": true, "list": false, "required": false, "placeholder": "", "show": true, "name": "should_store_message", "value": true, "display_name": "Store Messages", "advanced": true, "dynamic": false, "info": "Store the message in the history.", "title_case": false, "type": "bool", "_input_type": "BoolInput"}}, "description": "Display a chat message in the Playground.", "icon": "ChatOutput", "base_classes": ["Message"], "display_name": "Chat Output", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message", "display_name": "Message", "method": "message_response", "value": "__UNDEFINED__", "cache": true}], "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "ChatOutput-zBv53"}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 4429.812566227955, "y": 940.6072472757681}, "dragging": false}, {"id": "NotionUserList-wFEb1", "type": "genericNode", "position": {"x": 2390.6365450681037, "y": 694.4867003504073}, "data": {"type": "NotionUserList", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "import requests\nfrom typing import List, Dict\nfrom pydantic import BaseModel\nfrom langflow.io import Output\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import SecretStrInput\nfrom langflow.schema import Data\nfrom langflow.field_typing import Tool\nfrom langchain.tools import StructuredTool\n\n\nclass NotionUserList(LCToolComponent):\n    display_name = \"List Users \"\n    description = \"Retrieve users from Notion.\"\n    documentation = \"https://docs.langflow.org/integrations/notion/list-users\"\n    icon = \"NotionDirectoryLoader\"\n\n    inputs = [\n        SecretStrInput(\n            name=\"notion_secret\",\n            display_name=\"Notion Secret\",\n            info=\"The Notion integration token.\",\n            required=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"example_output\", display_name=\"Data\", method=\"run_model\"),\n        Output(name=\"example_tool_output\", display_name=\"Tool\", method=\"build_tool\"),\n    ]\n\n    class NotionUserListSchema(BaseModel):\n        pass\n\n    def run_model(self) -> List[Data]:\n        users = self._list_users()\n        records = []\n        combined_text = \"\"\n\n        for user in users:\n            output = \"User:\\n\"\n            for key, value in user.items():\n                output += f\"{key.replace('_', ' ').title()}: {value}\\n\"\n            output += \"________________________\\n\"\n\n            combined_text += output\n            records.append(Data(text=output, data=user))\n\n        self.status = records\n        return records\n\n    def build_tool(self) -> Tool:\n        return StructuredTool.from_function(\n            name=\"notion_list_users\",\n            description=\"Retrieve users from Notion.\",\n            func=self._list_users,\n            args_schema=self.NotionUserListSchema,\n        )\n\n    def _list_users(self) -> List[Dict]:\n        url = \"https://api.notion.com/v1/users\"\n        headers = {\n            \"Authorization\": f\"Bearer {self.notion_secret}\",\n            \"Notion-Version\": \"2022-06-28\",\n        }\n\n        response = requests.get(url, headers=headers)\n        response.raise_for_status()\n\n        data = response.json()\n        results = data[\"results\"]\n\n        users = []\n        for user in results:\n            user_data = {\n                \"id\": user[\"id\"],\n                \"type\": user[\"type\"],\n                \"name\": user.get(\"name\", \"\"),\n                \"avatar_url\": user.get(\"avatar_url\", \"\"),\n            }\n            users.append(user_data)\n\n        return users\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "notion_secret": {"load_from_db": false, "required": true, "placeholder": "", "show": true, "name": "notion_secret", "value": "", "display_name": "Notion Secret", "advanced": false, "input_types": ["Message"], "dynamic": false, "info": "The Notion integration token.", "title_case": false, "password": true, "type": "str", "_input_type": "SecretStrInput"}}, "description": "Retrieve users from Notion.", "icon": "NotionDirectoryLoader", "base_classes": ["Data", "Tool"], "display_name": "List Users ", "documentation": "https://docs.langflow.org/integrations/notion/list-users", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Data"], "selected": "Data", "name": "example_output", "display_name": "Data", "method": "run_model", "value": "__UNDEFINED__", "cache": true}, {"types": ["Tool"], "selected": "Tool", "name": "example_tool_output", "display_name": "Tool", "method": "build_tool", "value": "__UNDEFINED__", "cache": true, "hidden": true}], "field_order": ["notion_secret"], "beta": false, "edited": true, "lf_version": "1.0.17"}, "id": "NotionUserList-wFEb1", "description": "Retrieve users from Notion.", "display_name": "List Users "}, "selected": false, "width": 384, "height": 302, "positionAbsolute": {"x": 2390.6365450681037, "y": 694.4867003504073}, "dragging": false}, {"id": "ParseData-WKjW6", "type": "genericNode", "position": {"x": 2877.571533084884, "y": 856.8480898893301}, "data": {"type": "ParseData", "node": {"template": {"_type": "Component", "data": {"trace_as_metadata": true, "list": false, "trace_as_input": true, "required": false, "placeholder": "", "show": true, "name": "data", "value": "", "display_name": "Data", "advanced": false, "input_types": ["Data"], "dynamic": false, "info": "The data to convert to text.", "title_case": false, "type": "other", "_input_type": "DataInput"}, "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"Parse Data\"\n    description = \"Convert Data into plain text following a specified template.\"\n    icon = \"braces\"\n    name = \"ParseData\"\n\n    inputs = [\n        DataInput(name=\"data\", display_name=\"Data\", info=\"The data to convert to text.\"),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"Template\",\n            info=\"The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.\",\n            value=\"{text}\",\n        ),\n        StrInput(name=\"sep\", display_name=\"Separator\", advanced=True, value=\"\\n\"),\n    ]\n\n    outputs = [\n        Output(display_name=\"Text\", name=\"text\", method=\"parse_data\"),\n    ]\n\n    def parse_data(self) -> Message:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n\n        result_string = data_to_text(template, data, sep=self.sep)\n        self.status = result_string\n        return Message(text=result_string)\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "sep": {"trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "sep", "value": "\n", "display_name": "Separator", "advanced": true, "dynamic": false, "info": "", "title_case": false, "type": "str", "_input_type": "StrInput"}, "template": {"trace_as_input": true, "multiline": true, "trace_as_metadata": true, "load_from_db": false, "list": false, "required": false, "placeholder": "", "show": true, "name": "template", "value": "{text}", "display_name": "Template", "advanced": true, "input_types": ["Message"], "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {data} or any other key in the Data.", "title_case": false, "type": "str", "_input_type": "MultilineInput"}}, "description": "Convert Data into plain text following a specified template.", "icon": "braces", "base_classes": ["Message"], "display_name": "Parse Data", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "text", "display_name": "Text", "method": "parse_data", "value": "__UNDEFINED__", "cache": true}], "field_order": ["data", "template", "sep"], "beta": false, "edited": false, "lf_version": "1.0.17"}, "id": "ParseData-WKjW6", "showNode": false}, "selected": false, "width": 96, "height": 96, "positionAbsolute": {"x": 2877.571533084884, "y": 856.8480898893301}, "dragging": false}, {"id": "CurrentDateComponent-WOwNq", "type": "genericNode", "position": {"x": 536.7929500860405, "y": 617.6055631700241}, "data": {"type": "CurrentDateComponent", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from datetime import datetime\r\nfrom zoneinfo import ZoneInfo\r\nfrom typing import List\r\n\r\nfrom langflow.custom import Component\r\nfrom langflow.io import DropdownInput, Output\r\nfrom langflow.schema.message import Message\r\n\r\nclass CurrentDateComponent(Component):\r\n    display_name = \"Current Date 🕰️\"\r\n    description = \"Returns the current date and time in the selected timezone.\"\r\n    icon = \"clock\"\r\n\r\n    inputs = [\r\n        DropdownInput(\r\n            name=\"timezone\",\r\n            display_name=\"Timezone\",\r\n            options=[\r\n                \"UTC\",\r\n                \"US/Eastern\",\r\n                \"US/Central\",\r\n                \"US/Mountain\",\r\n                \"US/Pacific\",\r\n                \"Europe/London\",\r\n                \"Europe/Paris\",\r\n                \"Asia/Tokyo\",\r\n                \"Australia/Sydney\",\r\n                \"America/Sao_Paulo\",\r\n                \"America/Cuiaba\",\r\n            ],\r\n            value=\"UTC\",\r\n            info=\"Select the timezone for the current date and time.\",\r\n        ),\r\n    ]\r\n\r\n    outputs = [\r\n        Output(display_name=\"Current Date\", name=\"current_date\", method=\"get_current_date\"),\r\n    ]\r\n\r\n    def get_current_date(self) -> Message:\r\n        try:\r\n            tz = ZoneInfo(self.timezone)\r\n            current_date = datetime.now(tz).strftime(\"%Y-%m-%d %H:%M:%S %Z\")\r\n            result = f\"Current date and time in {self.timezone}: {current_date}\"\r\n            self.status = result\r\n            return Message(text=result)\r\n        except Exception as e:\r\n            error_message = f\"Error: {str(e)}\"\r\n            self.status = error_message\r\n            return Message(text=error_message)", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "timezone": {"trace_as_metadata": true, "options": ["UTC", "US/Eastern", "US/Central", "US/Mountain", "US/Pacific", "Europe/London", "Europe/Paris", "Asia/Tokyo", "Australia/Sydney", "America/Sao_Paulo", "America/Cuiaba"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "timezone", "value": "UTC", "display_name": "Timezone", "advanced": false, "dynamic": false, "info": "Select the timezone for the current date and time.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}}, "description": "Returns the current date and time in the selected timezone.", "icon": "clock", "base_classes": ["Message"], "display_name": "Current Date", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "current_date", "display_name": "Current Date", "method": "get_current_date", "value": "__UNDEFINED__", "cache": true}], "field_order": ["timezone"], "beta": false, "edited": true}, "id": "CurrentDateComponent-WOwNq", "showNode": false}, "selected": false, "width": 96, "height": 96, "dragging": false, "positionAbsolute": {"x": 536.7929500860405, "y": 617.6055631700241}}, {"id": "CurrentDateComponent-PZ8xJ", "type": "genericNode", "position": {"x": 2871.6341688682833, "y": 453.3374434097356}, "data": {"type": "CurrentDateComponent", "node": {"template": {"_type": "Component", "code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from datetime import datetime\r\nfrom zoneinfo import ZoneInfo\r\nfrom typing import List\r\n\r\nfrom langflow.custom import Component\r\nfrom langflow.io import DropdownInput, Output\r\nfrom langflow.schema.message import Message\r\n\r\nclass CurrentDateComponent(Component):\r\n    display_name = \"Current Date 🕰️\"\r\n    description = \"Returns the current date and time in the selected timezone.\"\r\n    icon = \"clock\"\r\n\r\n    inputs = [\r\n        DropdownInput(\r\n            name=\"timezone\",\r\n            display_name=\"Timezone\",\r\n            options=[\r\n                \"UTC\",\r\n                \"US/Eastern\",\r\n                \"US/Central\",\r\n                \"US/Mountain\",\r\n                \"US/Pacific\",\r\n                \"Europe/London\",\r\n                \"Europe/Paris\",\r\n                \"Asia/Tokyo\",\r\n                \"Australia/Sydney\",\r\n                \"America/Sao_Paulo\",\r\n                \"America/Cuiaba\",\r\n            ],\r\n            value=\"UTC\",\r\n            info=\"Select the timezone for the current date and time.\",\r\n        ),\r\n    ]\r\n\r\n    outputs = [\r\n        Output(display_name=\"Current Date\", name=\"current_date\", method=\"get_current_date\"),\r\n    ]\r\n\r\n    def get_current_date(self) -> Message:\r\n        try:\r\n            tz = ZoneInfo(self.timezone)\r\n            current_date = datetime.now(tz).strftime(\"%Y-%m-%d %H:%M:%S %Z\")\r\n            result = f\"Current date and time in {self.timezone}: {current_date}\"\r\n            self.status = result\r\n            return Message(text=result)\r\n        except Exception as e:\r\n            error_message = f\"Error: {str(e)}\"\r\n            self.status = error_message\r\n            return Message(text=error_message)", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "timezone": {"trace_as_metadata": true, "options": ["UTC", "US/Eastern", "US/Central", "US/Mountain", "US/Pacific", "Europe/London", "Europe/Paris", "Asia/Tokyo", "Australia/Sydney", "America/Sao_Paulo", "America/Cuiaba"], "combobox": false, "required": false, "placeholder": "", "show": true, "name": "timezone", "value": "UTC", "display_name": "Timezone", "advanced": false, "dynamic": false, "info": "Select the timezone for the current date and time.", "title_case": false, "type": "str", "_input_type": "DropdownInput"}}, "description": "Returns the current date and time in the selected timezone.", "icon": "clock", "base_classes": ["Message"], "display_name": "Current Date", "documentation": "", "custom_fields": {}, "output_types": [], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "current_date", "display_name": "Current Date", "method": "get_current_date", "value": "__UNDEFINED__", "cache": true}], "field_order": ["timezone"], "beta": false, "edited": true, "official": false}, "id": "CurrentDateComponent-PZ8xJ", "showNode": false}, "selected": false, "width": 96, "height": 96, "dragging": false, "positionAbsolute": {"x": 2871.6341688682833, "y": 453.3374434097356}}], "edges": [{"source": "TextInput-iJPEJ", "sourceHandle": "{œdataTypeœ:œTextInputœ,œidœ:œTextInput-iJPEJœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-19rub", "targetHandle": "{œfieldNameœ:œTRANSCRIPTœ,œidœ:œPrompt-19rubœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TRANSCRIPT", "id": "Prompt-19rub", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "TextInput", "id": "TextInput-iJPEJ", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-TextInput-iJPEJ{œdataTypeœ:œTextInputœ,œidœ:œTextInput-iJPEJœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-19rub{œfieldNameœ:œTRANSCRIPTœ,œidœ:œPrompt-19rubœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "NotionUserList-TvIKS", "sourceHandle": "{œdataTypeœ:œNotionUserListœ,œidœ:œNotionUserList-TvIKSœ,œnameœ:œexample_outputœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-aNk1v", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-aNk1vœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-aNk1v", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "NotionUserList", "id": "NotionUserList-TvIKS", "name": "example_output", "output_types": ["Data"]}}, "id": "reactflow__edge-NotionUserList-TvIKS{œdataTypeœ:œNotionUserListœ,œidœ:œNotionUserList-TvIKSœ,œnameœ:œexample_outputœ,œoutput_typesœ:[œDataœ]}-ParseData-aNk1v{œfieldNameœ:œdataœ,œidœ:œParseData-aNk1vœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "ParseData-aNk1v", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-aNk1vœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-19rub", "targetHandle": "{œfieldNameœ:œUSERSœ,œidœ:œPrompt-19rubœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "USERS", "id": "Prompt-19rub", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-aNk1v", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-ParseData-aNk1v{œdataTypeœ:œParseDataœ,œidœ:œParseData-aNk1vœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-19rub{œfieldNameœ:œUSERSœ,œidœ:œPrompt-19rubœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "Prompt-19rub", "sourceHandle": "{œdataTypeœ:œPromptœ,œidœ:œPrompt-19rubœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}", "target": "ToolCallingAgent-rVWeq", "targetHandle": "{œfieldNameœ:œsystem_promptœ,œidœ:œToolCallingAgent-rVWeqœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "system_prompt", "id": "ToolCallingAgent-rVWeq", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "Prompt", "id": "Prompt-19rub", "name": "prompt", "output_types": ["Message"]}}, "id": "reactflow__edge-Prompt-19rub{œdataTypeœ:œPromptœ,œidœ:œPrompt-19rubœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-ToolCallingAgent-rVWeq{œfieldNameœ:œsystem_promptœ,œidœ:œToolCallingAgent-rVWeqœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "NotionSearch-M66HF", "sourceHandle": "{œdataTypeœ:œNotionSearchœ,œidœ:œNotionSearch-M66HFœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}", "target": "ToolCallingAgent-rVWeq", "targetHandle": "{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-rVWeqœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "tools", "id": "ToolCallingAgent-rVWeq", "inputTypes": ["Tool", "BaseTool"], "type": "other"}, "sourceHandle": {"dataType": "NotionSearch", "id": "NotionSearch-M66HF", "name": "example_tool_output", "output_types": ["Tool"]}}, "id": "reactflow__edge-NotionSearch-M66HF{œdataTypeœ:œNotionSearchœ,œidœ:œNotionSearch-M66HFœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}-ToolCallingAgent-rVWeq{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-rVWeqœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "OpenAIModel-Ht8xI", "sourceHandle": "{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-Ht8xIœ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}", "target": "ToolCallingAgent-rVWeq", "targetHandle": "{œfieldNameœ:œllmœ,œidœ:œToolCallingAgent-rVWeqœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "llm", "id": "ToolCallingAgent-rVWeq", "inputTypes": ["LanguageModel"], "type": "other"}, "sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-Ht8xI", "name": "model_output", "output_types": ["LanguageModel"]}}, "id": "reactflow__edge-OpenAIModel-Ht8xI{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-Ht8xIœ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}-ToolCallingAgent-rVWeq{œfieldNameœ:œllmœ,œidœ:œToolCallingAgent-rVWeqœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "ToolCallingAgent-rVWeq", "sourceHandle": "{œdataTypeœ:œToolCallingAgentœ,œidœ:œToolCallingAgent-rVWeqœ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-Lbxk6", "targetHandle": "{œfieldNameœ:œTASK_LISTœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "TASK_LIST", "id": "Prompt-Lbxk6", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "ToolCallingAgent", "id": "ToolCallingAgent-rVWeq", "name": "response", "output_types": ["Message"]}}, "id": "reactflow__edge-ToolCallingAgent-rVWeq{œdataTypeœ:œToolCallingAgentœ,œidœ:œToolCallingAgent-rVWeqœ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}-Prompt-Lbxk6{œfieldNameœ:œTASK_LISTœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "OpenAIModel-OTfnt", "sourceHandle": "{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-OTfntœ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}", "target": "ToolCallingAgent-GurdE", "targetHandle": "{œfieldNameœ:œllmœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "llm", "id": "ToolCallingAgent-GurdE", "inputTypes": ["LanguageModel"], "type": "other"}, "sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-OTfnt", "name": "model_output", "output_types": ["LanguageModel"]}}, "id": "reactflow__edge-OpenAIModel-OTfnt{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-OTfntœ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}-ToolCallingAgent-GurdE{œfieldNameœ:œllmœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "Prompt-Lbxk6", "sourceHandle": "{œdataTypeœ:œPromptœ,œidœ:œPrompt-Lbxk6œ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}", "target": "ToolCallingAgent-GurdE", "targetHandle": "{œfieldNameœ:œsystem_promptœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "system_prompt", "id": "ToolCallingAgent-GurdE", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "Prompt", "id": "Prompt-Lbxk6", "name": "prompt", "output_types": ["Message"]}}, "id": "reactflow__edge-Prompt-Lbxk6{œdataTypeœ:œPromptœ,œidœ:œPrompt-Lbxk6œ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-ToolCallingAgent-GurdE{œfieldNameœ:œsystem_promptœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "AddContentToPage-vrAvx", "sourceHandle": "{œdataTypeœ:œAddContentToPageœ,œidœ:œAddContentToPage-vrAvxœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}", "target": "ToolCallingAgent-GurdE", "targetHandle": "{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "tools", "id": "ToolCallingAgent-GurdE", "inputTypes": ["Tool", "BaseTool"], "type": "other"}, "sourceHandle": {"dataType": "AddContentToPage", "id": "AddContentToPage-vrAvx", "name": "example_tool_output", "output_types": ["Tool"]}}, "id": "reactflow__edge-AddContentToPage-vrAvx{œdataTypeœ:œAddContentToPageœ,œidœ:œAddContentToPage-vrAvxœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}-ToolCallingAgent-GurdE{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "NotionPageCreator-Exc7f", "sourceHandle": "{œdataTypeœ:œNotionPageCreatorœ,œidœ:œNotionPageCreator-Exc7fœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}", "target": "ToolCallingAgent-GurdE", "targetHandle": "{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "tools", "id": "ToolCallingAgent-GurdE", "inputTypes": ["Tool", "BaseTool"], "type": "other"}, "sourceHandle": {"dataType": "NotionPageCreator", "id": "NotionPageCreator-Exc7f", "name": "example_tool_output", "output_types": ["Tool"]}}, "id": "reactflow__edge-NotionPageCreator-Exc7f{œdataTypeœ:œNotionPageCreatorœ,œidœ:œNotionPageCreator-Exc7fœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}-ToolCallingAgent-GurdE{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "NotionDatabaseProperties-IjzLV", "sourceHandle": "{œdataTypeœ:œNotionDatabasePropertiesœ,œidœ:œNotionDatabaseProperties-IjzLVœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}", "target": "ToolCallingAgent-GurdE", "targetHandle": "{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "tools", "id": "ToolCallingAgent-GurdE", "inputTypes": ["Tool", "BaseTool"], "type": "other"}, "sourceHandle": {"dataType": "NotionDatabaseProperties", "id": "NotionDatabaseProperties-IjzLV", "name": "example_tool_output", "output_types": ["Tool"]}}, "id": "reactflow__edge-NotionDatabaseProperties-IjzLV{œdataTypeœ:œNotionDatabasePropertiesœ,œidœ:œNotionDatabaseProperties-IjzLVœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}-ToolCallingAgent-GurdE{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "NotionPageUpdate-bexvy", "sourceHandle": "{œdataTypeœ:œNotionPageUpdateœ,œidœ:œNotionPageUpdate-bexvyœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}", "target": "ToolCallingAgent-GurdE", "targetHandle": "{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "tools", "id": "ToolCallingAgent-GurdE", "inputTypes": ["Tool", "BaseTool"], "type": "other"}, "sourceHandle": {"dataType": "NotionPageUpdate", "id": "NotionPageUpdate-bexvy", "name": "example_tool_output", "output_types": ["Tool"]}}, "id": "reactflow__edge-NotionPageUpdate-bexvy{œdataTypeœ:œNotionPageUpdateœ,œidœ:œNotionPageUpdate-bexvyœ,œnameœ:œexample_tool_outputœ,œoutput_typesœ:[œToolœ]}-ToolCallingAgent-GurdE{œfieldNameœ:œtoolsœ,œidœ:œToolCallingAgent-GurdEœ,œinputTypesœ:[œToolœ,œBaseToolœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "NotionSearch-EdSJb", "sourceHandle": "{œdataTypeœ:œNotionSearchœ,œidœ:œNotionSearch-EdSJbœ,œnameœ:œexample_outputœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-vYVwu", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-vYVwuœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-vYVwu", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "NotionSearch", "id": "NotionSearch-EdSJb", "name": "example_output", "output_types": ["Data"]}}, "id": "reactflow__edge-NotionSearch-EdSJb{œdataTypeœ:œNotionSearchœ,œidœ:œNotionSearch-EdSJbœ,œnameœ:œexample_outputœ,œoutput_typesœ:[œDataœ]}-ParseData-vYVwu{œfieldNameœ:œdataœ,œidœ:œParseData-vYVwuœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "className": ""}, {"source": "ParseData-vYVwu", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-vYVwuœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-Lbxk6", "targetHandle": "{œfieldNameœ:œDATABASESœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "DATABASES", "id": "Prompt-Lbxk6", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-vYVwu", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-ParseData-vYVwu{œdataTypeœ:œParseDataœ,œidœ:œParseData-vYVwuœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-Lbxk6{œfieldNameœ:œDATABASESœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "ToolCallingAgent-GurdE", "sourceHandle": "{œdataTypeœ:œToolCallingAgentœ,œidœ:œToolCallingAgent-GurdEœ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}", "target": "ChatOutput-zBv53", "targetHandle": "{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-zBv53œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_value", "id": "ChatOutput-zBv53", "inputTypes": ["Message"], "type": "str"}, "sourceHandle": {"dataType": "ToolCallingAgent", "id": "ToolCallingAgent-GurdE", "name": "response", "output_types": ["Message"]}}, "id": "reactflow__edge-ToolCallingAgent-GurdE{œdataTypeœ:œToolCallingAgentœ,œidœ:œToolCallingAgent-GurdEœ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-zBv53{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-zBv53œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "className": ""}, {"source": "NotionUserList-wFEb1", "sourceHandle": "{œdataTypeœ:œNotionUserListœ,œidœ:œNotionUserList-wFEb1œ,œnameœ:œexample_outputœ,œoutput_typesœ:[œDataœ]}", "target": "ParseData-WKjW6", "targetHandle": "{œfieldNameœ:œdataœ,œidœ:œParseData-WKjW6œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "data": {"targetHandle": {"fieldName": "data", "id": "ParseData-WKjW6", "inputTypes": ["Data"], "type": "other"}, "sourceHandle": {"dataType": "NotionUserList", "id": "NotionUserList-wFEb1", "name": "example_output", "output_types": ["Data"]}}, "id": "reactflow__edge-NotionUserList-wFEb1{œdataTypeœ:œNotionUserListœ,œidœ:œNotionUserList-wFEb1œ,œnameœ:œexample_outputœ,œoutput_typesœ:[œDataœ]}-ParseData-WKjW6{œfieldNameœ:œdataœ,œidœ:œParseData-WKjW6œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "className": ""}, {"source": "ParseData-WKjW6", "sourceHandle": "{œdataTypeœ:œParseDataœ,œidœ:œParseData-WKjW6œ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-Lbxk6", "targetHandle": "{œfieldNameœ:œUSERSœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "USERS", "id": "Prompt-Lbxk6", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "ParseData", "id": "ParseData-WKjW6", "name": "text", "output_types": ["Message"]}}, "id": "reactflow__edge-ParseData-WKjW6{œdataTypeœ:œParseDataœ,œidœ:œParseData-WKjW6œ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-Lbxk6{œfieldNameœ:œUSERSœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "className": ""}, {"source": "CurrentDateComponent-WOwNq", "sourceHandle": "{œdataTypeœ:œCurrentDateComponentœ,œidœ:œCurrentDateComponent-WOwNqœ,œnameœ:œcurrent_dateœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-19rub", "targetHandle": "{œfieldNameœ:œCURRENT_DATEœ,œidœ:œPrompt-19rubœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "CURRENT_DATE", "id": "Prompt-19rub", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "CurrentDateComponent", "id": "CurrentDateComponent-WOwNq", "name": "current_date", "output_types": ["Message"]}}, "id": "reactflow__edge-CurrentDateComponent-WOwNq{œdataTypeœ:œCurrentDateComponentœ,œidœ:œCurrentDateComponent-WOwNqœ,œnameœ:œcurrent_dateœ,œoutput_typesœ:[œMessageœ]}-Prompt-19rub{œfieldNameœ:œCURRENT_DATEœ,œidœ:œPrompt-19rubœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "className": ""}, {"source": "CurrentDateComponent-PZ8xJ", "sourceHandle": "{œdataTypeœ:œCurrentDateComponentœ,œidœ:œCurrentDateComponent-PZ8xJœ,œnameœ:œcurrent_dateœ,œoutput_typesœ:[œMessageœ]}", "target": "Prompt-Lbxk6", "targetHandle": "{œfieldNameœ:œCURRENT_DATEœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "CURRENT_DATE", "id": "Prompt-Lbxk6", "inputTypes": ["Message", "Text"], "type": "str"}, "sourceHandle": {"dataType": "CurrentDateComponent", "id": "CurrentDateComponent-PZ8xJ", "name": "current_date", "output_types": ["Message"]}}, "id": "reactflow__edge-CurrentDateComponent-PZ8xJ{œdataTypeœ:œCurrentDateComponentœ,œidœ:œCurrentDateComponent-PZ8xJœ,œnameœ:œcurrent_dateœ,œoutput_typesœ:[œMessageœ]}-Prompt-Lbxk6{œfieldNameœ:œCURRENT_DATEœ,œidœ:œPrompt-Lbxk6œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "className": ""}], "viewport": {"x": -65.48833753518215, "y": 119.49034539812101, "zoom": 0.5588906662759379}}, "description": "The Notion Agent for Meeting Notes is an AI-powered tool that automatically processes meeting transcripts and updates your Notion workspace accordingly. It identifies tasks, action items, and key points from your meetings, then creates new tasks or updates existing ones in Notion without manual input.\n\nTo use it, simply add your API Keys and provide a meeting transcript. The agent will analyze it, interact with your Notion workspace to make necessary updates, and give you a summary of actions taken. This streamlines your workflow, ensuring important meeting outcomes are captured and organized in Notion effortlessly.", "name": "Notion Agent - Meeting Notes ", "last_tested_version": "1.0.17.dev8", "endpoint_name": null, "is_component": false}