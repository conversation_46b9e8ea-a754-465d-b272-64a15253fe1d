---
title: Deploy Langflow on Render
slug: /deployment-render
---

This guide explains how to deploy Langflow on [Render](https://render.com/), a cloud platform for deploying web applications and APIs.

:::note
Langflow requires at least 2 GB of RAM to run, so it uses a **standard** Render instance. This may require a credit card. Review [Render's pricing](https://render.com/pricing) before proceeding.
:::

1. Click the following button to go to Render:

   [![Deploy to Render](/logos/render-deploy.svg)](https://render.com/deploy?repo=https%3A%2F%2Fgithub.com%2Flangflow-ai%2Flangflow%2Ftree%2Fdev)

2. Enter a blueprint name, and then select the branch for your `render.yaml` file.

3. Click **Deploy Blueprint**.

Wait for the deployment to complete.

Your Langflow instance is now ready to use.

