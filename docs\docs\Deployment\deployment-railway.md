---
title: Deploy Langflow on Railway
slug: /deployment-railway
---

This guide explains how to deploy Langflow on [Railway](https://railway.app/), a cloud infrastructure platform that provides auto-deploy, managed databases, and automatic scaling.

1. Click the following button to go to Railway:

   [![Deploy on Railway](/logos/railway-deploy.svg)](https://railway.app/template/JMXEWp?referralCode=MnPSdg)

2. Click **Deploy Now**. 
Railway automatically does the following:
   - Sets up the infrastructure.
   - Deploys Langflow.
   - Starts the application.

Wait for the deployment to complete.

Your Langflow instance is now ready to use.
