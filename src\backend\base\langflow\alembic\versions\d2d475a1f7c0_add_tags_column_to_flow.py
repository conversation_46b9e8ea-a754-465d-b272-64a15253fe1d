"""add tags column to flow

Revision ID: d2d475a1f7c0
Revises: d3dbf656a499
Create Date: 2024-10-03 13:33:59.517261

"""
from typing import Sequence, Union

import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.engine.reflection import Inspector

from langflow.utils import migration

# revision identifiers, used by Alembic.
revision: str = 'd2d475a1f7c0'
down_revision: Union[str, None] = 'd3dbf656a499'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('flow', schema=None) as batch_op:
        if not migration.column_exists(table_name='flow', column_name='tags', conn=conn):
            batch_op.add_column(sa.Column('tags', sa.JSON(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    conn = op.get_bind()
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('flow', schema=None) as batch_op:
        if migration.column_exists(table_name='flow', column_name='tags', conn=conn):
            batch_op.drop_column('tags')

    # ### end Alembic commands ###
