{"openapi": "3.1.0", "info": {"title": "Langflow", "version": "1.4.3"}, "paths": {"/api/v1/build/{flow_id}/vertices": {"post": {"tags": ["Cha<PERSON>"], "summary": "Retrieve Vertices Order", "description": "Retrieve the vertices order for a given flow.\n\nArgs:\n    flow_id (str): The ID of the flow.\n    background_tasks (BackgroundTasks): The background tasks.\n    data (Optional[FlowDataRequest], optional): The flow data. Defaults to None.\n    stop_component_id (str, optional): The ID of the stop component. Defaults to None.\n    start_component_id (str, optional): The ID of the start component. Defaults to None.\n    session (AsyncSession, optional): The session dependency.\n\nReturns:\n    VerticesOrderResponse: The response containing the ordered vertex IDs and the run ID.\n\nRaises:\n    HTTPException: If there is an error checking the build status.", "operationId": "retrieve_vertices_order_api_v1_build__flow_id__vertices_post", "deprecated": true, "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}, {"name": "data", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/FlowDataRequest"}, {"type": "null"}], "title": "Data"}}, {"name": "stop_component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stop Component Id"}}, {"name": "start_component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Component Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerticesOrderResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/build/{flow_id}/flow": {"post": {"tags": ["Cha<PERSON>"], "summary": "Build Flow", "description": "Build and process a flow, returning a job ID for event polling.\n\nThis endpoint requires authentication through the CurrentActiveUser dependency.\nFor public flows that don't require authentication, use the /build_public_tmp/flow_id/flow endpoint.\n\nArgs:\n    flow_id: UUID of the flow to build\n    background_tasks: Background tasks manager\n    inputs: Optional input values for the flow\n    data: Optional flow data\n    files: Optional files to include\n    stop_component_id: Optional ID of component to stop at\n    start_component_id: Optional ID of component to start from\n    log_builds: Whether to log the build process\n    current_user: The authenticated user\n    queue_service: Queue service for job management\n    flow_name: Optional name for the flow\n    event_delivery: Optional event delivery type - default is streaming\n\nReturns:\n    Dict with job_id that can be used to poll for build status", "operationId": "build_flow_api_v1_build__flow_id__flow_post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}, {"name": "stop_component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stop Component Id"}}, {"name": "start_component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Component Id"}}, {"name": "log_builds", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Log Builds"}}, {"name": "flow_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Flow Name"}}, {"name": "event_delivery", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/EventDeliveryType", "default": "polling"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_build_flow_api_v1_build__flow_id__flow_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/build/{job_id}/events": {"get": {"tags": ["Cha<PERSON>"], "summary": "Get Build Events", "description": "Get events for a specific build job.", "operationId": "get_build_events_api_v1_build__job_id__events_get", "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Job Id"}}, {"name": "event_delivery", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/EventDeliveryType", "default": "streaming"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/build/{job_id}/cancel": {"post": {"tags": ["Cha<PERSON>"], "summary": "Cancel Build", "description": "Cancel a specific build job.", "operationId": "cancel_build_api_v1_build__job_id__cancel_post", "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelFlowResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/build/{flow_id}/vertices/{vertex_id}": {"post": {"tags": ["Cha<PERSON>"], "summary": "Build Vertex", "description": "Build a vertex instead of the entire graph.\n\nArgs:\n    flow_id (str): The ID of the flow.\n    vertex_id (str): The ID of the vertex to build.\n    background_tasks (BackgroundTasks): The background tasks dependency.\n    inputs (Optional[InputValueRequest], optional): The input values for the vertex. Defaults to None.\n    files (List[str], optional): The files to use. Defaults to None.\n    current_user (Any, optional): The current user dependency. Defaults to Depends(get_current_active_user).\n\nReturns:\n    VertexBuildResponse: The response containing the built vertex information.\n\nRaises:\n    HTTPException: If there is an error building the vertex.", "operationId": "build_vertex_api_v1_build__flow_id__vertices__vertex_id__post", "deprecated": true, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}, {"name": "vertex_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Vertex Id"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_build_vertex_api_v1_build__flow_id__vertices__vertex_id__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VertexBuildResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/build/{flow_id}/{vertex_id}/stream": {"get": {"tags": ["Cha<PERSON>"], "summary": "Build Vertex Stream", "description": "Build a vertex instead of the entire graph.\n\nThis function is responsible for building a single vertex instead of the entire graph.\nIt takes the `flow_id` and `vertex_id` as required parameters, and an optional `session_id`.\nIt also depends on the `ChatService` and `SessionService` services.\n\nIf `session_id` is not provided, it retrieves the graph from the cache using the `chat_service`.\nIf `session_id` is provided, it loads the session data using the `session_service`.\n\nOnce the graph is obtained, it retrieves the specified vertex using the `vertex_id`.\nIf the vertex does not support streaming, an error is raised.\nIf the vertex has a built result, it sends the result as a chunk.\nIf the vertex is not frozen or not built, it streams the vertex data.\nIf the vertex has a result, it sends the result as a chunk.\nIf none of the above conditions are met, an error is raised.\n\nIf any exception occurs during the process, an error message is sent.\nFinally, the stream is closed.\n\nReturns:\n    A `StreamingResponse` object with the streamed vertex data in text/event-stream format.\n\nRaises:\n    HTTPException: If an error occurs while building the vertex.", "operationId": "build_vertex_stream_api_v1_build__flow_id___vertex_id__stream_get", "deprecated": true, "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}, {"name": "vertex_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Vertex Id"}}], "responses": {"200": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/build_public_tmp/{flow_id}/flow": {"post": {"tags": ["Cha<PERSON>"], "summary": "Build Public Tmp", "description": "Build a public flow without requiring authentication.\n\nThis endpoint is specifically for public flows that don't require authentication.\nIt uses a client_id cookie to create a deterministic flow ID for tracking purposes.\n\nThe endpoint:\n1. Verifies the requested flow is marked as public in the database\n2. Creates a deterministic UUID based on client_id and flow_id\n3. Uses the flow owner's permissions to build the flow\n\nRequirements:\n- The flow must be marked as PUBLIC in the database\n- The request must include a client_id cookie\n\nArgs:\n    flow_id: UUID of the public flow to build\n    background_tasks: Background tasks manager\n    inputs: Optional input values for the flow\n    data: Optional flow data\n    files: Optional files to include\n    stop_component_id: Optional ID of component to stop at\n    start_component_id: Optional ID of component to start from\n    log_builds: Whether to log the build process\n    flow_name: Optional name for the flow\n    request: FastAPI request object (needed for cookie access)\n    queue_service: Queue service for job management\n    event_delivery: Optional event delivery type - default is streaming\n\nReturns:\n    Dict with job_id that can be used to poll for build status", "operationId": "build_public_tmp_api_v1_build_public_tmp__flow_id__flow_post", "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}, {"name": "stop_component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stop Component Id"}}, {"name": "start_component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Component Id"}}, {"name": "log_builds", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Log Builds"}}, {"name": "flow_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Flow Name"}}, {"name": "event_delivery", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/EventDeliveryType", "default": "polling"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_build_public_tmp_api_v1_build_public_tmp__flow_id__flow_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/all": {"get": {"tags": ["Base"], "summary": "Get All", "description": "Retrieve all component types with compression for better performance.\n\nReturns a compressed response containing all available component types.", "operationId": "get_all_api_v1_all_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/run/{flow_id_or_name}": {"post": {"tags": ["Base"], "summary": "Simplified Run Flow", "description": "Executes a specified flow by ID with support for streaming and telemetry.\n\nThis endpoint executes a flow identified by ID or name, with options for streaming the response\nand tracking execution metrics. It handles both streaming and non-streaming execution modes.\n\nArgs:\n    background_tasks (BackgroundTasks): FastAPI background task manager\n    flow (FlowRead | None): The flow to execute, loaded via dependency\n    input_request (SimplifiedAPIRequest | None): Input parameters for the flow\n    stream (bool): Whether to stream the response\n    api_key_user (UserRead): Authenticated user from API key\n    request (Request): The incoming HTTP request\n\nReturns:\n    Union[StreamingResponse, RunResponse]: Either a streaming response for real-time results\n    or a RunResponse with the complete execution results\n\nRaises:\n    HTTPException: For flow not found (404) or invalid input (400)\n    APIException: For internal execution errors (500)\n\nNotes:\n    - Supports both streaming and non-streaming execution modes\n    - Tracks execution time and success/failure via telemetry\n    - <PERSON>les graceful client disconnection in streaming mode\n    - Provides detailed error handling with appropriate HTTP status codes\n    - In streaming mode, uses EventManager to handle events:\n        - \"add_message\": New messages during execution\n        - \"token\": Individual tokens during streaming\n        - \"end\": Final execution result", "operationId": "simplified_run_flow_api_v1_run__flow_id_or_name__post", "security": [{"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id_or_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Flow Id Or Name"}}, {"name": "stream", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Stream"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "string", "format": "uuid"}, {"type": "null"}], "title": "User Id"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/SimplifiedAPIRequest"}, {"type": "null"}], "title": "Input Request"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webhook/{flow_id_or_name}": {"post": {"tags": ["Base"], "summary": "Webhook Run Flow", "description": "Run a flow using a webhook request.\n\nArgs:\n    flow (Flow, optional): The flow to be executed. Defaults to Depends(get_flow_by_id).\n    user (User): The flow user.\n    request (Request): The incoming HTTP request.\n    background_tasks (BackgroundTasks): The background tasks manager.\n\nReturns:\n    dict: A dictionary containing the status of the task.\n\nRaises:\n    HTTPException: If the flow is not found or if there is an error processing the request.", "operationId": "webhook_run_flow_api_v1_webhook__flow_id_or_name__post", "parameters": [{"name": "flow_id_or_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Flow Id Or Name"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "string", "format": "uuid"}, {"type": "null"}], "title": "User Id"}}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Webhook Run Flow Api V1 Webhook  Flow Id Or Name  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/run/advanced/{flow_id}": {"post": {"tags": ["Base"], "summary": "Experimental Run Flow", "description": "Executes a specified flow by ID with optional input values, output selection, tweaks, and streaming capability.\n\nThis endpoint supports running flows with caching to enhance performance and efficiency.\n\n### Parameters:\n- `flow_id` (str): The unique identifier of the flow to be executed.\n- `inputs` (List[InputValueRequest], optional): A list of inputs specifying the input values and components\n  for the flow. Each input can target specific components and provide custom values.\n- `outputs` (List[str], optional): A list of output names to retrieve from the executed flow.\n  If not provided, all outputs are returned.\n- `tweaks` (Optional[Tweaks], optional): A dictionary of tweaks to customize the flow execution.\n  The tweaks can be used to modify the flow's parameters and components.\n  Tweaks can be overridden by the input values.\n- `stream` (bool, optional): Specifies whether the results should be streamed. Defaults to False.\n- `session_id` (Union[None, str], optional): An optional session ID to utilize existing session data for the flow\n  execution.\n- `api_key_user` (User): The user associated with the current API key. Automatically resolved from the API key.\n\n### Returns:\nA `RunResponse` object containing the selected outputs (or all if not specified) of the executed flow\nand the session ID.\nThe structure of the response accommodates multiple inputs, providing a nested list of outputs for each input.\n\n### Raises:\nHTTPException: Indicates issues with finding the specified flow, invalid input formats, or internal errors during\nflow execution.\n\n### Example usage:\n```json\nPOST /run/flow_id\nx-api-key: YOUR_API_KEY\nPayload:\n{\n    \"inputs\": [\n        {\"components\": [\"component1\"], \"input_value\": \"value1\"},\n        {\"components\": [\"component3\"], \"input_value\": \"value2\"}\n    ],\n    \"outputs\": [\"Component Name\", \"component_id\"],\n    \"tweaks\": {\"parameter_name\": \"value\", \"Component Name\": {\"parameter_name\": \"value\"}, \"component_id\": {\"parameter_name\": \"value\"}}\n    \"stream\": false\n}\n```\n\nThis endpoint facilitates complex flow executions with customized inputs, outputs, and configurations,\ncatering to diverse application requirements.", "operationId": "experimental_run_flow_api_v1_run_advanced__flow_id__post", "security": [{"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_experimental_run_flow_api_v1_run_advanced__flow_id__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/process/{_flow_id}": {"post": {"tags": ["Base"], "summary": "Process", "description": "Endpoint to process an input with a given flow_id.", "operationId": "process_api_v1_process___flow_id__post", "security": [{"API key query": []}, {"API key header": []}], "parameters": [{"name": "_flow_id", "in": "path", "required": true, "schema": {"title": " Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "null", "title": "Response Process Api V1 Process   Flow Id  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/predict/{_flow_id}": {"post": {"tags": ["Base"], "summary": "Process", "description": "Endpoint to process an input with a given flow_id.", "operationId": "process_api_v1_predict___flow_id__post", "security": [{"API key query": []}, {"API key header": []}], "parameters": [{"name": "_flow_id", "in": "path", "required": true, "schema": {"title": " Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "null", "title": "Response Process Api V1 Predict   Flow Id  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/task/{_task_id}": {"get": {"tags": ["Base"], "summary": "Get Task Status", "description": "Get the status of a task by ID (Deprecated).\n\nThis endpoint is deprecated and will be removed in a future version.", "operationId": "get_task_status_api_v1_task___task_id__get", "deprecated": true, "parameters": [{"name": "_task_id", "in": "path", "required": true, "schema": {"type": "string", "title": " Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/upload/{flow_id}": {"post": {"tags": ["Base"], "summary": "Create Upload File", "description": "Upload a file for a specific flow (Deprecated).\n\nThis endpoint is deprecated and will be removed in a future version.", "operationId": "create_upload_file_api_v1_upload__flow_id__post", "deprecated": true, "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_create_upload_file_api_v1_upload__flow_id__post"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/langflow__api__v1__schemas__UploadFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/version": {"get": {"tags": ["Base"], "summary": "Get Version", "operationId": "get_version_api_v1_version_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/custom_component": {"post": {"tags": ["Base"], "summary": "Custom Component", "operationId": "custom_component_api_v1_custom_component_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomComponentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomComponentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/custom_component/update": {"post": {"tags": ["Base"], "summary": "Custom Component Update", "description": "Update a custom component with the provided code request.\n\nThis endpoint generates the CustomComponentFrontendNode normally but then runs the `update_build_config` method\non the latest version of the template.\nThis ensures that every time it runs, it has the latest version of the template.\n\nArgs:\n    code_request (CustomComponentRequest): The code request containing the updated code for the custom component.\n    user (User, optional): The user making the request. Defaults to the current active user.\n\nReturns:\n    dict: The updated custom component node.\n\nRaises:\n    HTTPException: If there's an error building or updating the component\n    SerializationError: If there's an error serializing the component to JSON", "operationId": "custom_component_update_api_v1_custom_component_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomComponentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/config": {"get": {"tags": ["Base"], "summary": "Get Config", "operationId": "get_config_api_v1_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigResponse"}}}}}}}, "/api/v1/validate/code": {"post": {"tags": ["Validate"], "summary": "Post Validate Code", "operationId": "post_validate_code_api_v1_validate_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Code"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeValidationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/validate/prompt": {"post": {"tags": ["Validate"], "summary": "Post Validate Prompt", "operationId": "post_validate_prompt_api_v1_validate_prompt_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidatePromptRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromptValidationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/store/check/": {"get": {"tags": ["Components Store"], "summary": "Check If Store Is Enabled", "operationId": "check_if_store_is_enabled_api_v1_store_check__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/store/check/api_key": {"get": {"tags": ["Components Store"], "summary": "Check If Store Has Api Key", "operationId": "check_if_store_has_api_key_api_v1_store_check_api_key_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/store/components/": {"post": {"tags": ["Components Store"], "summary": "Share Component", "operationId": "share_component_api_v1_store_components__post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreComponentCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateComponentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Components Store"], "summary": "Get Components", "operationId": "get_components_api_v1_store_components__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Component Id"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "private", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Private"}}, {"name": "is_component", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component"}}, {"name": "tags", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "title": "Tags"}}, {"name": "sort", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "title": "Sort"}}, {"name": "liked", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Liked"}}, {"name": "filter_by_user", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Filter By User"}}, {"name": "fields", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "title": "Fields"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListComponentResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/store/components/{component_id}": {"patch": {"tags": ["Components Store"], "summary": "Update Shared Component", "operationId": "update_shared_component_api_v1_store_components__component_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "component_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Component Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreComponentCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateComponentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Components Store"], "summary": "Download Component", "operationId": "download_component_api_v1_store_components__component_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "component_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Component Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DownloadComponentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/store/tags": {"get": {"tags": ["Components Store"], "summary": "Get Tags", "operationId": "get_tags_api_v1_store_tags_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/TagResponse"}, "type": "array", "title": "Response Get Tags Api V1 Store Tags Get"}}}}}}}, "/api/v1/store/users/likes": {"get": {"tags": ["Components Store"], "summary": "Get List Of Components Liked By User", "operationId": "get_list_of_components_liked_by_user_api_v1_store_users_likes_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UsersLikesResponse"}, "type": "array", "title": "Response Get List Of Components Liked By User Api V1 Store Users Likes Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/store/users/likes/{component_id}": {"post": {"tags": ["Components Store"], "summary": "Like Component", "operationId": "like_component_api_v1_store_users_likes__component_id__post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "component_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Component Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersLikesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/flows/": {"post": {"tags": ["Flows"], "summary": "Create Flow", "operationId": "create_flow_api_v1_flows__post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Flows"], "summary": "Read Flows", "description": "Retrieve a list of flows with pagination support.\n\nArgs:\n    current_user (User): The current authenticated user.\n    session (Session): The database session.\n    settings_service (SettingsService): The settings service.\n    components_only (bool, optional): Whether to return only components. Defaults to False.\n\n    get_all (bool, optional): Whether to return all flows without pagination. Defaults to True.\n    **This field must be True because of backward compatibility with the frontend - Release: 1.0.20**\n\n    folder_id (UUID, optional): The project ID. Defaults to None.\n    params (Params): Pagination parameters.\n    remove_example_flows (bool, optional): Whether to remove example flows. Defaults to False.\n    header_flows (bool, optional): Whether to return only specific headers of the flows. Defaults to False.\n\nReturns:\n    list[FlowRead] | Page[FlowRead] | list[FlowHeader]\n    A list of flows or a paginated response containing the list of flows or a list of flow headers.", "operationId": "read_flows_api_v1_flows__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "remove_example_flows", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Remove Example Flows"}}, {"name": "components_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Components Only"}}, {"name": "get_all", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Get All"}}, {"name": "folder_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id"}}, {"name": "header_flows", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Header Flows"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 50, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/FlowRead"}}, {"$ref": "#/components/schemas/Page_FlowRead_"}, {"type": "array", "items": {"$ref": "#/components/schemas/FlowHeader"}}], "title": "Response Read Flows Api V1 Flows  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Flows"], "summary": "Delete Multiple Flows", "description": "Delete multiple flows by their IDs.\n\nArgs:\n    flow_ids (List[str]): The list of flow IDs to delete.\n    user (User, optional): The user making the request. Defaults to the current active user.\n    db (Session, optional): The database session.\n\nReturns:\n    dict: A dictionary containing the number of flows deleted.", "operationId": "delete_multiple_flows_api_v1_flows__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Flow Ids"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/flows/{flow_id}": {"get": {"tags": ["Flows"], "summary": "Read Flow", "description": "Read a flow.", "operationId": "read_flow_api_v1_flows__flow_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Flows"], "summary": "Update Flow", "description": "Update a flow.", "operationId": "update_flow_api_v1_flows__flow_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Flows"], "summary": "Delete Flow", "description": "Delete a flow.", "operationId": "delete_flow_api_v1_flows__flow_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/flows/public_flow/{flow_id}": {"get": {"tags": ["Flows"], "summary": "Read Public Flow", "description": "Read a public flow.", "operationId": "read_public_flow_api_v1_flows_public_flow__flow_id__get", "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/flows/batch/": {"post": {"tags": ["Flows"], "summary": "Create Flows", "description": "Create multiple new flows.", "operationId": "create_flows_api_v1_flows_batch__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowListCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/FlowRead"}, "type": "array", "title": "Response Create Flows Api V1 Flows Batch  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/flows/upload/": {"post": {"tags": ["Flows"], "summary": "Upload File", "description": "Upload flows from a file.", "operationId": "upload_file_api_v1_flows_upload__post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "folder_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_api_v1_flows_upload__post"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlowRead"}, "title": "Response Upload File Api V1 Flows Upload  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/flows/download/": {"post": {"tags": ["Flows"], "summary": "Download Multiple File", "description": "Download all flows as a zip file.", "operationId": "download_multiple_file_api_v1_flows_download__post", "requestBody": {"content": {"application/json": {"schema": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Flow Ids"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/flows/basic_examples/": {"get": {"tags": ["Flows"], "summary": "Read Basic Examples", "description": "Retrieve a list of basic example flows.\n\nArgs:\n    session (Session): The database session.\n\nReturns:\n    list[FlowRead]: A list of basic example flows.", "operationId": "read_basic_examples_api_v1_flows_basic_examples__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/FlowRead"}, "type": "array", "title": "Response Read Basic Examples Api V1 Flows Basic Examples  Get"}}}}}}}, "/api/v1/users/": {"post": {"tags": ["Users"], "summary": "Add User", "description": "Add a new user to the database.", "operationId": "add_user_api_v1_users__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Users"], "summary": "Read All Users", "description": "Retrieve a list of users from the database with pagination.", "operationId": "read_all_users_api_v1_users__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/whoami": {"get": {"tags": ["Users"], "summary": "Read Current User", "description": "Retrieve the current user's data.", "operationId": "read_current_user_api_v1_users_whoami_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/users/{user_id}": {"patch": {"tags": ["Users"], "summary": "Patch User", "description": "Update an existing user's data.", "operationId": "patch_user_api_v1_users__user_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete User", "description": "Delete a user from the database.", "operationId": "delete_user_api_v1_users__user_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Delete User Api V1 Users  User Id  Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}/reset-password": {"patch": {"tags": ["Users"], "summary": "Reset Password", "description": "Reset a user's password.", "operationId": "reset_password_api_v1_users__user_id__reset_password_patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api_key/": {"get": {"tags": ["APIKey"], "summary": "Get Api Keys Route", "operationId": "get_api_keys_route_api_v1_api_key__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeysResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "post": {"tags": ["APIKey"], "summary": "Create Api Key Route", "operationId": "create_api_key_route_api_v1_api_key__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnmaskedApiKeyRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/api_key/{api_key_id}": {"delete": {"tags": ["APIKey"], "summary": "Delete Api Key Route", "operationId": "delete_api_key_route_api_v1_api_key__api_key_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "api_key_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Api Key Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api_key/store": {"post": {"tags": ["APIKey"], "summary": "Save Store Api Key", "operationId": "save_store_api_key_api_v1_api_key_store_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Login To Get Access Token", "operationId": "login_to_get_access_token_api_v1_login_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_login_to_get_access_token_api_v1_login_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auto_login": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Auto Login", "operationId": "auto_login_api_v1_auto_login_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refresh <PERSON>", "operationId": "refresh_token_api_v1_refresh_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Logout", "operationId": "logout_api_v1_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/variables/": {"get": {"tags": ["Variables"], "summary": "Read Variables", "description": "Read all variables.", "operationId": "read_variables_api_v1_variables__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/VariableRead"}, "type": "array", "title": "Response Read Variables Api V1 Variables  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "post": {"tags": ["Variables"], "summary": "Create Variable", "description": "Create a new variable.", "operationId": "create_variable_api_v1_variables__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/variables/{variable_id}": {"patch": {"tags": ["Variables"], "summary": "Update Variable", "description": "Update a variable.", "operationId": "update_variable_api_v1_variables__variable_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "variable_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Variable Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Variables"], "summary": "Delete Variable", "description": "Delete a variable.", "operationId": "delete_variable_api_v1_variables__variable_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "variable_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Variable Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/files/upload/{flow_id}": {"post": {"tags": ["Files"], "summary": "Upload File", "operationId": "upload_file_api_v1_files_upload__flow_id__post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_api_v1_files_upload__flow_id__post"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/langflow__api__v1__schemas__UploadFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/files/download/{flow_id}/{file_name}": {"get": {"tags": ["Files"], "summary": "Download File", "operationId": "download_file_api_v1_files_download__flow_id___file_name__get", "parameters": [{"name": "file_name", "in": "path", "required": true, "schema": {"type": "string", "title": "File Name"}}, {"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/files/images/{flow_id}/{file_name}": {"get": {"tags": ["Files"], "summary": "Download Image", "operationId": "download_image_api_v1_files_images__flow_id___file_name__get", "parameters": [{"name": "file_name", "in": "path", "required": true, "schema": {"type": "string", "title": "File Name"}}, {"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/files/profile_pictures/{folder_name}/{file_name}": {"get": {"tags": ["Files"], "summary": "Download Profile Picture", "operationId": "download_profile_picture_api_v1_files_profile_pictures__folder_name___file_name__get", "parameters": [{"name": "folder_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Folder Name"}}, {"name": "file_name", "in": "path", "required": true, "schema": {"type": "string", "title": "File Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/files/profile_pictures/list": {"get": {"tags": ["Files"], "summary": "List Profile Pictures", "operationId": "list_profile_pictures_api_v1_files_profile_pictures_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/files/list/{flow_id}": {"get": {"tags": ["Files"], "summary": "List Files", "operationId": "list_files_api_v1_files_list__flow_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/files/delete/{flow_id}/{file_name}": {"delete": {"tags": ["Files"], "summary": "Delete File", "operationId": "delete_file_api_v1_files_delete__flow_id___file_name__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "file_name", "in": "path", "required": true, "schema": {"type": "string", "title": "File Name"}}, {"name": "flow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitor/builds": {"get": {"tags": ["Monitor"], "summary": "Get Vertex Builds", "operationId": "get_vertex_builds_api_v1_monitor_builds_get", "parameters": [{"name": "flow_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VertexBuildMapModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Monitor"], "summary": "Delete Vertex Builds", "operationId": "delete_vertex_builds_api_v1_monitor_builds_delete", "parameters": [{"name": "flow_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitor/messages": {"get": {"tags": ["Monitor"], "summary": "Get Messages", "operationId": "get_messages_api_v1_monitor_messages_get", "parameters": [{"name": "flow_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Id"}}, {"name": "session_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}}, {"name": "sender", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender"}}, {"name": "sender_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender Name"}}, {"name": "order_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "timestamp", "title": "Order By"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MessageResponse"}, "title": "Response Get Messages Api V1 Monitor Messages Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Monitor"], "summary": "Delete Messages", "operationId": "delete_messages_api_v1_monitor_messages_delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Message Ids"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitor/messages/{message_id}": {"put": {"tags": ["Monitor"], "summary": "Update Message", "operationId": "update_message_api_v1_monitor_messages__message_id__put", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Message Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitor/messages/session/{old_session_id}": {"patch": {"tags": ["Monitor"], "summary": "Update Session Id", "operationId": "update_session_id_api_v1_monitor_messages_session__old_session_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "old_session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Old Session Id"}}, {"name": "new_session_id", "in": "query", "required": true, "schema": {"type": "string", "description": "The new session ID to update to", "title": "New Session Id"}, "description": "The new session ID to update to"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MessageResponse"}, "title": "Response Update Session Id Api V1 Monitor Messages Session  Old Session Id  Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitor/messages/session/{session_id}": {"delete": {"tags": ["Monitor"], "summary": "Delete Messages Session", "operationId": "delete_messages_session_api_v1_monitor_messages_session__session_id__delete", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitor/transactions": {"get": {"tags": ["Monitor"], "summary": "Get Transactions", "operationId": "get_transactions_api_v1_monitor_transactions_get", "parameters": [{"name": "flow_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Id"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_TransactionTable_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/folders/": {"get": {"tags": ["Folders"], "summary": "Read Folders Redirect", "description": "Redirect to the projects endpoint.", "operationId": "read_folders_redirect_api_v1_folders__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/FolderRead"}, "type": "array", "title": "Response Read Folders Redirect Api V1 Folders  Get"}}}}}}, "post": {"tags": ["Folders"], "summary": "Create Folder Redirect", "description": "Redirect to the projects endpoint.", "operationId": "create_folder_redirect_api_v1_folders__post", "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderRead"}}}}}}}, "/api/v1/folders/{folder_id}": {"get": {"tags": ["Folders"], "summary": "Read Folder Redirect", "description": "Redirect to the projects endpoint.", "operationId": "read_folder_redirect_api_v1_folders__folder_id__get", "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Folder Id"}}, {"name": "is_component", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Is Component"}}, {"name": "is_flow", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Is Flow"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string", "default": "", "title": "Search"}}, {"name": "page", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/FolderWithPaginatedFlows"}, {"$ref": "#/components/schemas/FolderReadWithFlows"}], "title": "Response Read Folder Redirect Api V1 Folders  Folder Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Folders"], "summary": "Update Folder Redirect", "description": "Redirect to the projects endpoint.", "operationId": "update_folder_redirect_api_v1_folders__folder_id__patch", "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Folder Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Folders"], "summary": "Delete Folder Redirect", "description": "Redirect to the projects endpoint.", "operationId": "delete_folder_redirect_api_v1_folders__folder_id__delete", "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Folder Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/folders/download/{folder_id}": {"get": {"tags": ["Folders"], "summary": "Download File Redirect", "description": "Redirect to the projects endpoint.", "operationId": "download_file_redirect_api_v1_folders_download__folder_id__get", "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Folder Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/folders/upload/": {"post": {"tags": ["Folders"], "summary": "Upload File Redirect", "description": "Redirect to the projects endpoint.", "operationId": "upload_file_redirect_api_v1_folders_upload__post", "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/FlowRead"}, "type": "array", "title": "Response Upload File Redirect Api V1 Folders Upload  Post"}}}}}}}, "/api/v1/projects/": {"get": {"tags": ["Projects"], "summary": "Read Projects", "operationId": "read_projects_api_v1_projects__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/FolderRead"}, "type": "array", "title": "Response Read Projects Api V1 Projects  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "post": {"tags": ["Projects"], "summary": "Create Project", "operationId": "create_project_api_v1_projects__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/projects/{project_id}": {"get": {"tags": ["Projects"], "summary": "Read Project", "operationId": "read_project_api_v1_projects__project_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}, {"name": "is_component", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Is Component"}}, {"name": "is_flow", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Is Flow"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string", "default": "", "title": "Search"}}, {"name": "page", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/FolderWithPaginatedFlows"}, {"$ref": "#/components/schemas/FolderReadWithFlows"}], "title": "Response Read Project Api V1 Projects  Project Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Projects"], "summary": "Update Project", "operationId": "update_project_api_v1_projects__project_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Projects"], "summary": "Delete Project", "operationId": "delete_project_api_v1_projects__project_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/download/{project_id}": {"get": {"tags": ["Projects"], "summary": "Download File", "description": "Download all flows from project as a zip file.", "operationId": "download_file_api_v1_projects_download__project_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/upload/": {"post": {"tags": ["Projects"], "summary": "Upload File", "description": "Upload flows from a file.", "operationId": "upload_file_api_v1_projects_upload__post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_api_v1_projects_upload__post"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/FlowRead"}, "type": "array", "title": "Response Upload File Api V1 Projects Upload  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/starter-projects/": {"get": {"tags": ["Flows"], "summary": "Get Starter Projects", "description": "Get a list of starter projects.", "operationId": "get_starter_projects_api_v1_starter_projects__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/GraphDump"}, "type": "array", "title": "Response Get Starter Projects Api V1 Starter Projects  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/voice/elevenlabs/voice_ids": {"get": {"tags": ["Voice"], "summary": "Get Elevenlabs Voice Ids", "description": "Get available voice IDs from ElevenLabs API.", "operationId": "get_elevenlabs_voice_ids_api_v1_voice_elevenlabs_voice_ids_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/mcp/sse": {"get": {"tags": ["mcp"], "summary": "<PERSON><PERSON>", "operationId": "handle_sse_api_v1_mcp_sse_get", "responses": {"200": {"description": "Successful Response"}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v1/mcp/": {"post": {"tags": ["mcp"], "summary": "Handle Messages", "operationId": "handle_messages_api_v1_mcp__post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/mcp/project/{project_id}": {"get": {"tags": ["mcp_projects"], "summary": "List Project Tools", "description": "List all tools in a project that are enabled for MCP.", "operationId": "list_project_tools_api_v1_mcp_project__project_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}, {"name": "mcp_enabled", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Mcp Enabled"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MCPSettings"}, "title": "Response List Project Tools Api V1 Mcp Project  Project Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["mcp_projects"], "summary": "Handle Project Messages", "description": "Handle POST messages for a project-specific MCP server.", "operationId": "handle_project_messages_api_v1_mcp_project__project_id__post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["mcp_projects"], "summary": "Update Project Mcp Settings", "description": "Update the MCP settings of all flows in a project.", "operationId": "update_project_mcp_settings_api_v1_mcp_project__project_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MCPSettings"}, "title": "Settings"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp/project/{project_id}/sse": {"get": {"tags": ["mcp_projects"], "summary": "Handle Project Sse", "description": "Handle SSE connections for a specific project.", "operationId": "handle_project_sse_api_v1_mcp_project__project_id__sse_get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"text/html": {"schema": {"type": "string"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp/project/{project_id}/": {"post": {"tags": ["mcp_projects"], "summary": "Handle Project Messages With Slash", "description": "Handle POST messages for a project-specific MCP server with trailing slash.", "operationId": "handle_project_messages_with_slash_api_v1_mcp_project__project_id___post", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v2/files/": {"get": {"tags": ["Files"], "summary": "List Files", "description": "List the files available to the current user.", "operationId": "list_files_api_v2_files__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/langflow__services__database__models__file__model__File"}, "type": "array", "title": "Response List Files Api V2 Files  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "post": {"tags": ["Files"], "summary": "Upload User File", "description": "Upload a file for the current user and track it in the database.", "operationId": "upload_user_file_api_v2_files__post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_user_file_api_v2_files__post"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/langflow__api__schemas__UploadFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "delete": {"tags": ["Files"], "summary": "Delete All Files", "description": "Delete all files for the current user.", "operationId": "delete_all_files_api_v2_files__delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v2/files": {"get": {"tags": ["Files"], "summary": "List Files", "description": "List the files available to the current user.", "operationId": "list_files_api_v2_files_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/langflow__services__database__models__file__model__File"}, "type": "array", "title": "Response List Files Api V2 Files Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "post": {"tags": ["Files"], "summary": "Upload User File", "description": "Upload a file for the current user and track it in the database.", "operationId": "upload_user_file_api_v2_files_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_user_file_api_v2_files_post"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/langflow__api__schemas__UploadFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "delete": {"tags": ["Files"], "summary": "Delete All Files", "description": "Delete all files for the current user.", "operationId": "delete_all_files_api_v2_files_delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v2/files/batch/": {"post": {"tags": ["Files"], "summary": "Download Files Batch", "description": "Download multiple files as a zip file by their IDs.", "operationId": "download_files_batch_api_v2_files_batch__post", "requestBody": {"content": {"application/json": {"schema": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "File Ids"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}, "delete": {"tags": ["Files"], "summary": "Delete Files Batch", "description": "Delete multiple files by their IDs.", "operationId": "delete_files_batch_api_v2_files_batch__delete", "requestBody": {"content": {"application/json": {"schema": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "File Ids"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/v2/files/{file_id}": {"get": {"tags": ["Files"], "summary": "Download File", "description": "Download a file by its ID.", "operationId": "download_file_api_v2_files__file_id__get", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "File Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Files"], "summary": "Edit File Name", "description": "Edit the name of a file by its ID.", "operationId": "edit_file_name_api_v2_files__file_id__put", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "File Id"}}, {"name": "name", "in": "query", "required": true, "schema": {"type": "string", "title": "Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/langflow__api__schemas__UploadFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Files"], "summary": "Delete File", "description": "Delete a file by its ID.", "operationId": "delete_file_api_v2_files__file_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}], "parameters": [{"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "File Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mcp/sse": {"get": {"tags": ["mcp"], "summary": "<PERSON><PERSON>", "operationId": "handle_sse_api_mcp_sse_get", "responses": {"200": {"description": "Successful Response"}}, "security": [{"OAuth2PasswordBearer": []}, {"API key query": []}, {"API key header": []}]}}, "/api/mcp/": {"post": {"tags": ["mcp"], "summary": "Handle Messages", "operationId": "handle_messages_api_mcp__post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"tags": ["Health Check"], "summary": "Health", "operationId": "health_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health_check": {"get": {"tags": ["Health Check"], "summary": "Health Check", "operationId": "health_check_health_check_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/logs-stream": {"get": {"tags": ["Log"], "summary": "Stream Logs", "description": "HTTP/2 Server-Sent-Event (SSE) endpoint for streaming logs.\n\nIt establishes a long-lived connection to the server and receives log messages in real-time.\nThe client should use the header \"Accept: text/event-stream\".", "operationId": "stream_logs_logs_stream_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/logs": {"get": {"tags": ["Log"], "summary": "Logs", "operationId": "logs_logs_get", "parameters": [{"name": "lines_before", "in": "query", "required": false, "schema": {"type": "integer", "description": "The number of logs before the timestamp or the last log", "default": 0, "title": "Lines Before"}, "description": "The number of logs before the timestamp or the last log"}, {"name": "lines_after", "in": "query", "required": false, "schema": {"type": "integer", "description": "The number of logs after the timestamp", "default": 0, "title": "Lines After"}, "description": "The number of logs after the timestamp"}, {"name": "timestamp", "in": "query", "required": false, "schema": {"type": "integer", "description": "The timestamp to start getting logs from", "default": 0, "title": "Timestamp"}, "description": "The timestamp to start getting logs from"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AccessTypeEnum": {"type": "string", "enum": ["PRIVATE", "PUBLIC"], "title": "AccessTypeEnum"}, "ApiKeyCreate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}, "total_uses": {"type": "integer", "title": "Total Uses", "default": 0}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "user_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "User Id"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At"}}, "type": "object", "title": "ApiKeyCreate"}, "ApiKeyCreateRequest": {"properties": {"api_key": {"type": "string", "title": "Api Key"}}, "type": "object", "required": ["api_key"], "title": "ApiKeyCreateRequest"}, "ApiKeyRead": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}, "total_uses": {"type": "integer", "title": "Total Uses", "default": 0}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "api_key": {"type": "string", "title": "Api Key"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "api_key", "user_id", "created_at"], "title": "ApiKeyRead"}, "ApiKeysResponse": {"properties": {"total_count": {"type": "integer", "title": "Total Count"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}, "api_keys": {"items": {"$ref": "#/components/schemas/ApiKeyRead"}, "type": "array", "title": "A<PERSON> <PERSON>"}}, "type": "object", "required": ["total_count", "user_id", "api_keys"], "title": "ApiKeysResponse"}, "BaseModel": {"properties": {}, "type": "object", "title": "BaseModel"}, "Body_build_flow_api_v1_build__flow_id__flow_post": {"properties": {"inputs": {"anyOf": [{"$ref": "#/components/schemas/InputValueRequest"}, {"type": "null"}]}, "data": {"anyOf": [{"$ref": "#/components/schemas/FlowDataRequest"}, {"type": "null"}]}, "files": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Files"}}, "type": "object", "title": "Body_build_flow_api_v1_build__flow_id__flow_post"}, "Body_build_public_tmp_api_v1_build_public_tmp__flow_id__flow_post": {"properties": {"inputs": {"anyOf": [{"$ref": "#/components/schemas/InputValueRequest"}, {"type": "null"}]}, "data": {"anyOf": [{"$ref": "#/components/schemas/FlowDataRequest"}, {"type": "null"}]}, "files": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Files"}}, "type": "object", "title": "Body_build_public_tmp_api_v1_build_public_tmp__flow_id__flow_post"}, "Body_build_vertex_api_v1_build__flow_id__vertices__vertex_id__post": {"properties": {"inputs": {"anyOf": [{"$ref": "#/components/schemas/InputValueRequest"}, {"type": "null"}]}, "files": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Files"}}, "type": "object", "title": "Body_build_vertex_api_v1_build__flow_id__vertices__vertex_id__post"}, "Body_create_upload_file_api_v1_upload__flow_id__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_create_upload_file_api_v1_upload__flow_id__post"}, "Body_experimental_run_flow_api_v1_run_advanced__flow_id__post": {"properties": {"inputs": {"anyOf": [{"items": {"$ref": "#/components/schemas/InputValueRequest"}, "type": "array"}, {"type": "null"}], "title": "Inputs"}, "outputs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Outputs"}, "tweaks": {"anyOf": [{"$ref": "#/components/schemas/Tweaks"}, {"type": "null"}]}, "stream": {"type": "boolean", "title": "Stream", "default": false}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}}, "type": "object", "title": "Body_experimental_run_flow_api_v1_run_advanced__flow_id__post"}, "Body_login_to_get_access_token_api_v1_login_post": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "^password$"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_login_to_get_access_token_api_v1_login_post"}, "Body_upload_file_api_v1_files_upload__flow_id__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_api_v1_files_upload__flow_id__post"}, "Body_upload_file_api_v1_flows_upload__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_api_v1_flows_upload__post"}, "Body_upload_file_api_v1_projects_upload__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_api_v1_projects_upload__post"}, "Body_upload_user_file_api_v2_files__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_user_file_api_v2_files__post"}, "Body_upload_user_file_api_v2_files_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_user_file_api_v2_files_post"}, "CancelFlowResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["success", "message"], "title": "CancelFlowResponse", "description": "Response model for flow build cancellation."}, "ChatOutputResponse": {"properties": {"message": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"type": "string"}, {"type": "object"}]}, "type": "array"}], "title": "Message"}, "sender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender", "default": "Machine"}, "sender_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender Name", "default": "AI"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "stream_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stream Url"}, "component_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Component Id"}, "files": {"items": {"$ref": "#/components/schemas/langflow__utils__schemas__File"}, "type": "array", "title": "Files", "default": []}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["message", "type"], "title": "ChatOutputResponse", "description": "Chat output response schema."}, "Code": {"properties": {"code": {"type": "string", "title": "Code"}}, "type": "object", "required": ["code"], "title": "Code"}, "CodeContent": {"type": "object"}, "CodeValidationResponse": {"properties": {"imports": {"type": "object", "title": "Imports"}, "function": {"type": "object", "title": "Function"}}, "type": "object", "required": ["imports", "function"], "title": "CodeValidationResponse"}, "ConfigResponse": {"properties": {"feature_flags": {"$ref": "#/components/schemas/FeatureFlags"}, "serialization_max_items_lenght": {"type": "integer", "title": "Serialization Max Items Lenght", "default": 100}, "serialization_max_text_length": {"type": "integer", "title": "Serialization Max Text Length", "default": 2000}, "frontend_timeout": {"type": "integer", "title": "Frontend Timeout"}, "auto_saving": {"type": "boolean", "title": "Auto Saving"}, "auto_saving_interval": {"type": "integer", "title": "Auto Saving Interval"}, "health_check_max_retries": {"type": "integer", "title": "Health Check Max Retries"}, "max_file_size_upload": {"type": "integer", "title": "Max File Size Upload"}, "webhook_polling_interval": {"type": "integer", "title": "Webhook Polling Interval"}, "public_flow_cleanup_interval": {"type": "integer", "title": "Public Flow Cleanup Interval"}, "public_flow_expiration": {"type": "integer", "title": "Public Flow Expiration"}, "event_delivery": {"type": "string", "enum": ["polling", "streaming", "direct"], "title": "Event Delivery"}}, "type": "object", "required": ["feature_flags", "frontend_timeout", "auto_saving", "auto_saving_interval", "health_check_max_retries", "max_file_size_upload", "webhook_polling_interval", "public_flow_cleanup_interval", "public_flow_expiration", "event_delivery"], "title": "ConfigResponse"}, "ContentBlock": {"properties": {"title": {"type": "string", "title": "Title"}, "contents": {"items": {"type": "object"}, "type": "array", "title": "Contents"}, "allow_markdown": {"type": "boolean", "title": "Allow Markdown", "default": true}, "media_url": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Media Url"}}, "type": "object", "required": ["title", "contents"], "title": "ContentBlock", "description": "A block of content that can contain different types of content."}, "CreateComponentResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["id"], "title": "CreateComponentResponse"}, "CustomComponentRequest": {"properties": {"code": {"type": "string", "title": "Code"}, "frontend_node": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Frontend Node"}}, "type": "object", "required": ["code"], "title": "CustomComponentRequest"}, "CustomComponentResponse": {"properties": {"data": {"type": "object", "title": "Data"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["data", "type"], "title": "CustomComponentResponse"}, "DownloadComponentResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}}, "type": "object", "required": ["id", "name", "description", "data", "is_component"], "title": "DownloadComponentResponse"}, "EdgeData": {"properties": {"source": {"type": "string", "title": "Source"}, "target": {"type": "string", "title": "Target"}, "data": {"$ref": "#/components/schemas/EdgeDataDetails"}}, "type": "object", "title": "EdgeData"}, "EdgeDataDetails": {"properties": {"sourceHandle": {"$ref": "#/components/schemas/SourceHandleDict"}, "targetHandle": {"$ref": "#/components/schemas/TargetHandleDict"}}, "type": "object", "required": ["sourceHandle", "targetHandle"], "title": "EdgeDataDetails"}, "ErrorContent": {"type": "object"}, "ErrorLog": {"properties": {"errorMessage": {"type": "string", "title": "Errormessage"}, "stackTrace": {"type": "string", "title": "Stacktrace"}}, "type": "object", "required": ["errorMessage", "stackTrace"], "title": "ErrorLog"}, "EventDeliveryType": {"type": "string", "enum": ["streaming", "direct", "polling"], "title": "EventDeliveryType"}, "FeatureFlags": {"properties": {"mvp_components": {"type": "boolean", "title": "Mvp Components", "default": false}}, "additionalProperties": false, "type": "object", "title": "FeatureFlags"}, "Flow": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}, "icon_bg_color": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon Bg Color"}, "gradient": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gradient"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component", "default": false}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "webhook": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Webhook", "description": "Can be used on the webhook endpoint", "default": false}, "endpoint_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Endpoint Name"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "default": []}, "locked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Locked", "default": false}, "mcp_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Mcp Enabled", "description": "Can be exposed in the MCP server", "default": false}, "action_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Name", "description": "The name of the action associated with the flow"}, "action_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Description", "description": "The description of the action associated with the flow"}, "access_type": {"$ref": "#/components/schemas/AccessTypeEnum", "default": "PRIVATE"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "user_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "User Id"}, "folder_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id"}, "fs_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fs Path"}}, "type": "object", "required": ["name", "user_id"], "title": "Flow"}, "FlowCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}, "icon_bg_color": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon Bg Color"}, "gradient": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gradient"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component", "default": false}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "webhook": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Webhook", "description": "Can be used on the webhook endpoint", "default": false}, "endpoint_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Endpoint Name"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "locked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Locked", "default": false}, "mcp_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Mcp Enabled", "description": "Can be exposed in the MCP server", "default": false}, "action_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Name", "description": "The name of the action associated with the flow"}, "action_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Description", "description": "The description of the action associated with the flow"}, "access_type": {"$ref": "#/components/schemas/AccessTypeEnum", "default": "PRIVATE"}, "user_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "User Id"}, "folder_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id"}, "fs_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fs Path"}}, "type": "object", "required": ["name"], "title": "FlowCreate"}, "FlowDataRequest": {"properties": {"nodes": {"items": {"type": "object"}, "type": "array", "title": "Nodes"}, "edges": {"items": {"type": "object"}, "type": "array", "title": "<PERSON>s"}, "viewport": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Viewport"}}, "type": "object", "required": ["nodes", "edges"], "title": "FlowDataRequest"}, "FlowHeader": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the flow"}, "name": {"type": "string", "title": "Name", "description": "The name of the flow"}, "folder_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id", "description": "The ID of the folder containing the flow. None if not associated with a folder"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component", "description": "Flag indicating whether the flow is a component"}, "endpoint_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Endpoint Name", "description": "The name of the endpoint associated with this flow"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A description of the flow"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data", "description": "The data of the component, if is_component is True"}, "access_type": {"anyOf": [{"$ref": "#/components/schemas/AccessTypeEnum"}, {"type": "null"}], "description": "The access type of the flow"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "The tags of the flow"}, "mcp_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Mcp Enabled", "description": "Flag indicating whether the flow is exposed in the MCP server"}, "action_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Name", "description": "The name of the action associated with the flow"}, "action_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Description", "description": "The description of the action associated with the flow"}}, "type": "object", "required": ["id", "name"], "title": "FlowHeader", "description": "Model representing a header for a flow - Without the data."}, "FlowListCreate": {"properties": {"flows": {"items": {"$ref": "#/components/schemas/FlowCreate"}, "type": "array", "title": "Flows"}}, "type": "object", "required": ["flows"], "title": "FlowListCreate"}, "FlowRead": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}, "icon_bg_color": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon Bg Color"}, "gradient": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gradient"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component", "default": false}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "webhook": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Webhook", "description": "Can be used on the webhook endpoint", "default": false}, "endpoint_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Endpoint Name"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "The tags of the flow"}, "locked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Locked", "default": false}, "mcp_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Mcp Enabled", "description": "Can be exposed in the MCP server", "default": false}, "action_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Name", "description": "The name of the action associated with the flow"}, "action_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Description", "description": "The description of the action associated with the flow"}, "access_type": {"$ref": "#/components/schemas/AccessTypeEnum", "default": "PRIVATE"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "user_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "User Id"}, "folder_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id"}}, "type": "object", "required": ["name", "id", "user_id", "folder_id"], "title": "FlowRead"}, "FlowUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}, "folder_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id"}, "endpoint_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Endpoint Name"}, "mcp_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Mcp Enabled"}, "locked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Locked"}, "action_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Name"}, "action_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Description"}, "access_type": {"anyOf": [{"$ref": "#/components/schemas/AccessTypeEnum"}, {"type": "null"}]}, "fs_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fs Path"}}, "type": "object", "title": "FlowUpdate"}, "FolderCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "components_list": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Components List"}, "flows_list": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Flows List"}}, "type": "object", "required": ["name"], "title": "FolderCreate"}, "FolderRead": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "parent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Id"}}, "type": "object", "required": ["name", "id", "parent_id"], "title": "FolderRead"}, "FolderReadWithFlows": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "parent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Id"}, "flows": {"items": {"$ref": "#/components/schemas/FlowRead"}, "type": "array", "title": "Flows", "default": []}}, "type": "object", "required": ["name", "id", "parent_id"], "title": "FolderReadWithFlows"}, "FolderUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "parent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Id"}, "components": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Components"}, "flows": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Flows"}}, "type": "object", "title": "FolderUpdate"}, "FolderWithPaginatedFlows": {"properties": {"folder": {"$ref": "#/components/schemas/FolderRead"}, "flows": {"$ref": "#/components/schemas/Page_Flow_"}}, "type": "object", "required": ["folder", "flows"], "title": "FolderWithPaginatedFlows"}, "FrontendNodeRequest-Input": {"properties": {"template": {"type": "object", "title": "Template"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}, "is_input": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Input"}, "is_output": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Output"}, "is_composition": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Composition"}, "base_classes": {"items": {"type": "string"}, "type": "array", "title": "Base Classes"}, "name": {"type": "string", "title": "Name", "default": ""}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Name", "default": ""}, "priority": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Priority"}, "documentation": {"type": "string", "title": "Documentation", "default": ""}, "minimized": {"type": "boolean", "title": "Minimized", "default": false}, "custom_fields": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Custom Fields"}, "output_types": {"items": {"type": "string"}, "type": "array", "title": "Output Types", "default": []}, "full_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Path"}, "pinned": {"type": "boolean", "title": "Pinned", "default": false}, "conditional_paths": {"items": {"type": "string"}, "type": "array", "title": "Conditional Paths", "default": []}, "frozen": {"type": "boolean", "title": "Frozen", "default": false}, "outputs": {"items": {"$ref": "#/components/schemas/Output"}, "type": "array", "title": "Outputs", "default": []}, "field_order": {"items": {"type": "string"}, "type": "array", "title": "Field Order", "default": []}, "beta": {"type": "boolean", "title": "Beta", "default": false}, "legacy": {"type": "boolean", "title": "Legacy", "default": false}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "edited": {"type": "boolean", "title": "Edited", "default": false}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "default": {}}, "tool_mode": {"type": "boolean", "title": "Tool Mode", "default": false}}, "type": "object", "required": ["template", "base_classes"], "title": "FrontendNodeRequest"}, "FrontendNodeRequest-Output": {"properties": {"template": {"type": "object", "title": "Template"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}, "is_input": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Input"}, "is_output": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Output"}, "is_composition": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Composition"}, "base_classes": {"items": {"type": "string"}, "type": "array", "title": "Base Classes"}, "name": {"type": "string", "title": "Name", "default": ""}, "display_name": {"type": "string", "title": "Display Name"}, "priority": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Priority"}, "documentation": {"type": "string", "title": "Documentation", "default": ""}, "minimized": {"type": "boolean", "title": "Minimized", "default": false}, "custom_fields": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Custom Fields"}, "output_types": {"items": {"type": "string"}, "type": "array", "title": "Output Types", "default": []}, "full_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Path"}, "pinned": {"type": "boolean", "title": "Pinned", "default": false}, "conditional_paths": {"items": {"type": "string"}, "type": "array", "title": "Conditional Paths", "default": []}, "frozen": {"type": "boolean", "title": "Frozen", "default": false}, "outputs": {"items": {"$ref": "#/components/schemas/Output"}, "type": "array", "title": "Outputs", "default": []}, "field_order": {"items": {"type": "string"}, "type": "array", "title": "Field Order", "default": []}, "beta": {"type": "boolean", "title": "Beta", "default": false}, "legacy": {"type": "boolean", "title": "Legacy", "default": false}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "edited": {"type": "boolean", "title": "Edited", "default": false}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "default": {}}, "tool_mode": {"type": "boolean", "title": "Tool Mode", "default": false}}, "type": "object", "required": ["template", "base_classes"], "title": "FrontendNodeRequest"}, "GraphData": {"properties": {"nodes": {"items": {"$ref": "#/components/schemas/NodeData"}, "type": "array", "title": "Nodes"}, "edges": {"items": {"$ref": "#/components/schemas/EdgeData"}, "type": "array", "title": "<PERSON>s"}, "viewport": {"$ref": "#/components/schemas/ViewPort"}}, "type": "object", "required": ["nodes", "edges"], "title": "GraphData"}, "GraphDump": {"properties": {"data": {"$ref": "#/components/schemas/GraphData"}, "is_component": {"type": "boolean", "title": "Is Component"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "endpoint_name": {"type": "string", "title": "Endpoint Name"}}, "type": "object", "title": "GraphDump"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HeaderDict": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}}, "type": "object", "title": "HeaderDict"}, "HealthResponse": {"properties": {"status": {"type": "string", "title": "Status", "default": "nok"}, "chat": {"type": "string", "title": "Cha<PERSON>", "default": "error check the server logs"}, "db": {"type": "string", "title": "Db", "default": "error check the server logs"}}, "type": "object", "title": "HealthResponse"}, "InputValueRequest": {"properties": {"components": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Components", "default": []}, "input_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Input Value"}, "session": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session"}, "type": {"anyOf": [{"type": "string", "enum": ["chat", "text", "any"]}, {"type": "null"}], "title": "Type", "description": "Defines on which components the input value should be applied. 'any' applies to all input components.", "default": "any"}}, "additionalProperties": false, "type": "object", "title": "InputValueRequest", "examples": [{"components": ["components_id", "Component Name"], "input_value": "input_value", "session": "session_id"}, {"components": ["Component Name"], "input_value": "input_value"}, {"input_value": "input_value"}, {"components": ["Component Name"], "input_value": "input_value", "session": "session_id"}, {"input_value": "input_value", "session": "session_id"}, {"input_value": "input_value", "type": "chat"}, {"input_value": "{\"key\": \"value\"}", "type": "json"}]}, "JSONContent": {"type": "object"}, "ListComponentResponse": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "liked_by_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Liked By Count"}, "liked_by_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Liked By User"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}, "user_created": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "User Created", "default": {}}, "tags": {"anyOf": [{"items": {"$ref": "#/components/schemas/TagResponse"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "downloads_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Downloads Count"}, "last_tested_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Tested Version"}, "private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Private"}}, "type": "object", "title": "ListComponentResponse"}, "ListComponentResponseModel": {"properties": {"count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Count", "default": 0}, "authorized": {"type": "boolean", "title": "Authorized"}, "results": {"anyOf": [{"items": {"$ref": "#/components/schemas/ListComponentResponse"}, "type": "array"}, {"type": "null"}], "title": "Results"}}, "type": "object", "required": ["authorized", "results"], "title": "ListComponentResponseModel"}, "Log": {"properties": {"name": {"type": "string", "title": "Name"}, "message": {"anyOf": [{"type": "string"}, {"type": "object"}, {"items": {}, "type": "array"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"$ref": "#/components/schemas/BaseModel"}, {"$ref": "#/components/schemas/PlaygroundEvent"}, {"type": "null"}], "title": "Message"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["name", "message", "type"], "title": "Log"}, "MCPSettings": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "mcp_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Mcp Enabled"}, "action_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Name"}, "action_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Description"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["id"], "title": "MCPSettings", "description": "Model representing MCP settings for a flow."}, "MediaContent": {"type": "object"}, "MessageRead": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "sender": {"type": "string", "title": "Sender"}, "sender_name": {"type": "string", "title": "Sender Name"}, "session_id": {"type": "string", "title": "Session Id"}, "text": {"type": "string", "title": "Text"}, "files": {"items": {"type": "string"}, "type": "array", "title": "Files"}, "error": {"type": "boolean", "title": "Error", "default": false}, "edit": {"type": "boolean", "title": "Edit", "default": false}, "properties": {"$ref": "#/components/schemas/Properties"}, "category": {"type": "string", "title": "Category", "default": "message"}, "content_blocks": {"items": {"$ref": "#/components/schemas/ContentBlock"}, "type": "array", "title": "Content Blocks"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "flow_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Id"}}, "type": "object", "required": ["sender", "sender_name", "session_id", "text", "id", "flow_id"], "title": "MessageRead"}, "MessageResponse": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Id"}, "flow_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "sender": {"type": "string", "title": "Sender"}, "sender_name": {"type": "string", "title": "Sender Name"}, "session_id": {"type": "string", "title": "Session Id"}, "text": {"type": "string", "title": "Text"}, "files": {"items": {"type": "string"}, "type": "array", "title": "Files", "default": []}, "edit": {"type": "boolean", "title": "Edit"}, "duration": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration"}, "properties": {"anyOf": [{"$ref": "#/components/schemas/Properties"}, {"type": "null"}]}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}, "content_blocks": {"anyOf": [{"items": {"$ref": "#/components/schemas/ContentBlock"}, "type": "array"}, {"type": "null"}], "title": "Content Blocks"}}, "type": "object", "required": ["sender", "sender_name", "session_id", "text", "edit"], "title": "MessageResponse"}, "MessageUpdate": {"properties": {"text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text"}, "sender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender"}, "sender_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender Name"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "files": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Files"}, "edit": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Edit"}, "error": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Error"}, "properties": {"anyOf": [{"$ref": "#/components/schemas/Properties"}, {"type": "null"}]}}, "type": "object", "title": "MessageUpdate"}, "NodeData": {"properties": {"id": {"type": "string", "title": "Id"}, "data": {"type": "object", "title": "Data"}, "dragging": {"type": "boolean", "title": "Dragging"}, "height": {"type": "integer", "title": "Height"}, "width": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "position": {"$ref": "#/components/schemas/Position"}, "positionAbsolute": {"$ref": "#/components/schemas/Position"}, "selected": {"type": "boolean", "title": "Selected"}, "parent_node_id": {"type": "string", "title": "Parent Node Id"}, "type": {"$ref": "#/components/schemas/NodeTypeEnum"}}, "type": "object", "required": ["id", "data"], "title": "NodeData"}, "NodeTypeEnum": {"type": "string", "enum": ["noteNode", "genericNode"], "title": "NodeTypeEnum"}, "Output": {"properties": {"types": {"items": {"type": "string"}, "type": "array", "title": "Types", "default": []}, "selected": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Selected"}, "name": {"type": "string", "title": "Name", "description": "The name of the field."}, "hidden": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Hidden"}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Name"}, "method": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Method"}, "value": {"anyOf": [{}, {"type": "null"}], "title": "Value", "default": "__UNDEFINED__"}, "cache": {"type": "boolean", "title": "<PERSON><PERSON>", "default": true}, "required_inputs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Required Inputs"}, "allows_loop": {"type": "boolean", "title": "Allows Loop", "default": false}, "options": {"anyOf": [{"$ref": "#/components/schemas/OutputOptions"}, {"type": "null"}]}, "tool_mode": {"type": "boolean", "title": "Tool Mode", "default": true}}, "type": "object", "required": ["name"], "title": "Output"}, "OutputOptions": {"properties": {"filter": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter"}}, "type": "object", "title": "OutputOptions"}, "OutputValue": {"properties": {"message": {"anyOf": [{"$ref": "#/components/schemas/ErrorLog"}, {"$ref": "#/components/schemas/StreamURL"}, {"type": "object"}, {"items": {}, "type": "array"}, {"type": "string"}], "title": "Message"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["message", "type"], "title": "OutputValue"}, "Page_FlowRead_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/FlowRead"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "page", "size"], "title": "Page[FlowRead]"}, "Page_Flow_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Flow"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "page", "size"], "title": "Page[Flow]"}, "Page_TransactionTable_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/TransactionTable"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "page", "size"], "title": "Page[TransactionTable]"}, "PlaygroundEvent": {"properties": {"properties": {"anyOf": [{"$ref": "#/components/schemas/Properties"}, {"type": "null"}]}, "sender_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender Name"}, "content_blocks": {"anyOf": [{"items": {"$ref": "#/components/schemas/ContentBlock"}, "type": "array"}, {"type": "null"}], "title": "Content Blocks"}, "format_type": {"type": "string", "enum": ["default", "error", "warning", "info"], "title": "Format Type", "default": "default"}, "files": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Files"}, "text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text"}, "timestamp": {"type": "string", "title": "Timestamp"}, "id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "string"}, {"type": "null"}], "title": "Id"}}, "additionalProperties": true, "type": "object", "title": "PlaygroundEvent"}, "Position": {"properties": {"x": {"type": "number", "title": "X"}, "y": {"type": "number", "title": "Y"}}, "type": "object", "required": ["x", "y"], "title": "Position"}, "PromptValidationResponse": {"properties": {"input_variables": {"items": {}, "type": "array", "title": "Input Variables"}, "frontend_node": {"anyOf": [{"$ref": "#/components/schemas/FrontendNodeRequest-Output"}, {"type": "null"}]}}, "type": "object", "required": ["input_variables"], "title": "PromptValidationResponse"}, "Properties": {"properties": {"text_color": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text Color"}, "background_color": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Background Color"}, "edited": {"type": "boolean", "title": "Edited", "default": false}, "source": {"$ref": "#/components/schemas/Source"}, "icon": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Icon"}, "allow_markdown": {"type": "boolean", "title": "Allow Markdown", "default": false}, "positive_feedback": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Positive Feedback"}, "state": {"type": "string", "enum": ["partial", "complete"], "title": "State", "default": "complete"}, "targets": {"items": {}, "type": "array", "title": "Targets", "default": []}}, "type": "object", "title": "Properties"}, "ResultData": {"properties": {"results": {"anyOf": [{}, {"type": "null"}], "title": "Results"}, "artifacts": {"anyOf": [{}, {"type": "null"}], "title": "Artifacts"}, "outputs": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Outputs"}, "logs": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Logs"}, "messages": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatOutputResponse"}, "type": "array"}, {"type": "null"}], "title": "Messages"}, "timedelta": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Timedelta"}, "duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Duration"}, "component_display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Component Display Name"}, "component_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Component Id"}, "used_frozen_result": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Used Frozen Result", "default": false}}, "type": "object", "title": "ResultData"}, "ResultDataResponse": {"type": "object"}, "RunOutputs": {"properties": {"inputs": {"type": "object", "title": "Inputs"}, "outputs": {"items": {"anyOf": [{"$ref": "#/components/schemas/ResultData"}, {"type": "null"}]}, "type": "array", "title": "Outputs"}}, "type": "object", "title": "RunOutputs"}, "RunResponse": {"properties": {"outputs": {"anyOf": [{"items": {"$ref": "#/components/schemas/RunOutputs"}, "type": "array"}, {"type": "null"}], "title": "Outputs", "default": []}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}}, "type": "object", "title": "RunResponse", "description": "Run response schema."}, "SimplifiedAPIRequest": {"properties": {"input_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Input Value", "description": "The input value"}, "input_type": {"anyOf": [{"type": "string", "enum": ["chat", "text", "any"]}, {"type": "null"}], "title": "Input Type", "description": "The input type", "default": "chat"}, "output_type": {"anyOf": [{"type": "string", "enum": ["chat", "text", "any", "debug"]}, {"type": "null"}], "title": "Output Type", "description": "The output type", "default": "chat"}, "output_component": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Output Component", "description": "If there are multiple output components, you can specify the component to get the output from.", "default": ""}, "tweaks": {"anyOf": [{"$ref": "#/components/schemas/Tweaks"}, {"type": "null"}], "description": "The tweaks"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "The session id"}}, "type": "object", "title": "SimplifiedAPIRequest"}, "Source": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id", "description": "The id of the source component."}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Name", "description": "The display name of the source component."}, "source": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source", "description": "The source of the message. Normally used to display the model name (e.g. 'gpt-4o')"}}, "type": "object", "title": "Source"}, "SourceHandleDict": {"properties": {"baseClasses": {"items": {"type": "string"}, "type": "array", "title": "Baseclasses"}, "dataType": {"type": "string", "title": "Datatype"}, "id": {"type": "string", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "output_types": {"items": {"type": "string"}, "type": "array", "title": "Output Types"}}, "type": "object", "title": "SourceHandleDict"}, "StoreComponentCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "data": {"type": "object", "title": "Data"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "parent": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent"}, "is_component": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Component"}, "last_tested_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Tested Version"}, "private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Private", "default": true}}, "type": "object", "required": ["name", "description", "data", "tags", "is_component"], "title": "StoreComponentCreate"}, "StreamURL": {"properties": {"location": {"type": "string", "title": "Location"}}, "type": "object", "required": ["location"], "title": "StreamURL"}, "TagResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}}, "type": "object", "required": ["id", "name"], "title": "TagResponse"}, "TargetHandleDict": {"properties": {"fieldName": {"type": "string", "title": "Fieldname"}, "id": {"type": "string", "title": "Id"}, "inputTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Inputtypes"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["fieldName", "id", "inputTypes", "type"], "title": "TargetHandleDict"}, "TaskStatusResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "result": {"anyOf": [{}, {"type": "null"}], "title": "Result"}}, "type": "object", "required": ["status"], "title": "TaskStatusResponse", "description": "Task status response schema."}, "TextContent": {"type": "object"}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>"}, "token_type": {"type": "string", "title": "Token Type"}}, "type": "object", "required": ["access_token", "refresh_token", "token_type"], "title": "Token"}, "ToolContent": {"type": "object"}, "TransactionTable": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "vertex_id": {"type": "string", "title": "Vertex Id"}, "target_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Target Id"}, "inputs": {"type": "object", "title": "Inputs"}, "outputs": {"type": "object", "title": "Outputs"}, "status": {"type": "string", "title": "Status"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id"}, "id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Id"}}, "type": "object", "required": ["vertex_id", "status", "flow_id"], "title": "TransactionTable"}, "Tweaks": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "object"}]}, "type": "object", "title": "Tweaks", "description": "A dictionary of tweaks to adjust the flow's execution. Allows customizing flow behavior dynamically. All tweaks are overridden by the input values.", "examples": [{"Component Name": {"parameter_name": "value"}, "component_id": {"parameter_name": "value"}, "parameter_name": "value"}]}, "UnmaskedApiKeyRead": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}, "total_uses": {"type": "integer", "title": "Total Uses", "default": 0}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "api_key": {"type": "string", "title": "Api Key"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}}, "type": "object", "required": ["id", "api_key", "user_id"], "title": "UnmaskedApiKeyRead"}, "UpdateCustomComponentRequest": {"properties": {"code": {"type": "string", "title": "Code"}, "frontend_node": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Frontend Node"}, "field": {"type": "string", "title": "Field"}, "field_value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"type": "object"}, {"items": {}, "type": "array"}, {"type": "null"}], "title": "Field Value"}, "template": {"type": "object", "title": "Template"}, "tool_mode": {"type": "boolean", "title": "Tool Mode", "default": false}}, "type": "object", "required": ["code", "field", "template"], "title": "UpdateCustomComponentRequest"}, "UserCreate": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "optins": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {"github_starred": false, "dialog_dismissed": false, "discord_clicked": false}}}, "type": "object", "required": ["username", "password"], "title": "UserCreate"}, "UserRead": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "username": {"type": "string", "title": "Username"}, "profile_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Profile Image"}, "store_api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Store Api Key"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_superuser": {"type": "boolean", "title": "Is Superuser"}, "create_at": {"type": "string", "format": "date-time", "title": "Create At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At"}, "optins": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["username", "profile_image", "store_api_key", "is_active", "is_superuser", "create_at", "updated_at", "last_login_at"], "title": "UserRead"}, "UserUpdate": {"properties": {"username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "profile_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Profile Image"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "is_superuser": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Superuser"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At"}, "optins": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "title": "UserUpdate"}, "UsersLikesResponse": {"properties": {"likes_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Likes Count"}, "liked_by_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Liked By User"}}, "type": "object", "required": ["likes_count", "liked_by_user"], "title": "UsersLikesResponse"}, "UsersResponse": {"properties": {"total_count": {"type": "integer", "title": "Total Count"}, "users": {"items": {"$ref": "#/components/schemas/UserRead"}, "type": "array", "title": "Users"}}, "type": "object", "required": ["total_count", "users"], "title": "UsersResponse"}, "ValidatePromptRequest": {"properties": {"name": {"type": "string", "title": "Name"}, "template": {"type": "string", "title": "Template"}, "custom_fields": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Custom Fields"}, "frontend_node": {"anyOf": [{"$ref": "#/components/schemas/FrontendNodeRequest-Input"}, {"type": "null"}]}}, "type": "object", "required": ["name", "template"], "title": "ValidatePromptRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VariableCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the variable"}, "value": {"type": "string", "title": "Value", "description": "Encrypted value of the variable"}, "default_fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "Type of the variable"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Creation time of the variable"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Creation time of the variable"}}, "type": "object", "required": ["name", "value", "default_fields"], "title": "VariableCreate"}, "VariableRead": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the variable"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "Type of the variable"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Value", "description": "Encrypted value of the variable"}, "default_fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Default fields for the variable"}}, "type": "object", "required": ["id"], "title": "VariableRead"}, "VariableUpdate": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the variable"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Value", "description": "Encrypted value of the variable"}, "default_fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Default fields for the variable"}}, "type": "object", "required": ["id"], "title": "VariableUpdate"}, "VertexBuildMapModel": {"properties": {"vertex_builds": {"additionalProperties": {"items": {"$ref": "#/components/schemas/VertexBuildTable"}, "type": "array"}, "type": "object", "title": "Vertex Builds"}}, "type": "object", "required": ["vertex_builds"], "title": "VertexBuildMapModel"}, "VertexBuildResponse": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id"}, "inactivated_vertices": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Inactivated Vertices"}, "next_vertices_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Next Vertices Ids"}, "top_level_vertices": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Top Level Vertices"}, "valid": {"type": "boolean", "title": "<PERSON><PERSON>"}, "params": {"anyOf": [{}, {"type": "null"}], "title": "Params"}, "data": {"type": "object"}, "timestamp": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Timestamp"}}, "type": "object", "required": ["valid", "data"], "title": "VertexBuildResponse"}, "VertexBuildTable": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "id": {"type": "string", "title": "Id"}, "data": {"type": "object", "title": "Data"}, "artifacts": {"type": "object", "title": "Artifacts"}, "params": {"type": "string", "title": "Params"}, "valid": {"type": "boolean", "title": "<PERSON><PERSON>"}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id"}, "build_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Build Id"}}, "type": "object", "required": ["id", "valid", "flow_id"], "title": "VertexBuildTable"}, "VerticesOrderResponse": {"properties": {"ids": {"items": {"type": "string"}, "type": "array", "title": "Ids"}, "run_id": {"type": "string", "format": "uuid", "title": "Run Id"}, "vertices_to_run": {"items": {"type": "string"}, "type": "array", "title": "Vertices To Run"}}, "type": "object", "required": ["ids", "run_id", "vertices_to_run"], "title": "VerticesOrderResponse"}, "ViewPort": {"properties": {"x": {"type": "number", "title": "X"}, "y": {"type": "number", "title": "Y"}, "zoom": {"type": "number", "title": "Zoom"}}, "type": "object", "required": ["x", "y", "zoom"], "title": "ViewPort"}, "langflow__api__schemas__UploadFileResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "path": {"type": "string", "format": "path", "title": "Path"}, "size": {"type": "integer", "title": "Size"}, "provider": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Provider"}}, "type": "object", "required": ["id", "name", "path", "size"], "title": "UploadFileResponse", "description": "File upload response schema."}, "langflow__api__v1__schemas__UploadFileResponse": {"properties": {"flowId": {"type": "string", "title": "Flowid"}, "file_path": {"type": "string", "format": "path", "title": "File Path"}}, "type": "object", "required": ["flowId", "file_path"], "title": "UploadFileResponse", "description": "Upload file response schema."}, "langflow__services__database__models__file__model__File": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}, "name": {"type": "string", "title": "Name"}, "path": {"type": "string", "title": "Path"}, "size": {"type": "integer", "title": "Size"}, "provider": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Provider"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["user_id", "name", "path", "size"], "title": "File"}, "langflow__utils__schemas__File": {"properties": {"path": {"type": "string", "title": "Path"}, "name": {"type": "string", "title": "Name"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["path", "name", "type"], "title": "File", "description": "File schema."}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "api/v1/login"}}}, "API key query": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "x-api-key"}, "API key header": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key"}}}}